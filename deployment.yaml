apiVersion: apps/v1
kind: Deployment
metadata:
  name: ai
  namespace: topthink
  labels:
    app: ai
spec:
  replicas: 2
  selector:
    matchLabels:
      app: ai
  template:
    metadata:
      labels:
        app: ai
    spec:
      initContainers:
        - name: ai-init
          image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/ai:IMAGE_TAG
          imagePullPolicy: Always
          args:
            - 'app:init'
          env:
            - name: PHP_APP_TOKEN
              value: "a20cde8268ef4a54d1b369ee973109f8"
            - name: PHP_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: host
            - name: PHP_DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: user
            - name: PHP_DB_PASS
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: password
            - name: PHP_REDIS_HOST
              value: redis-master.thirdparty
            - name: PHP_CLOUD_ENABLE
              value: "true"
            - name: PHP_CLOUD_CLIENT_ID
              value: a64bedb71f5a389870a64232ae6d74b0
            - name: PHP_CLOUD_CLIENT_SECRET
              value: 9c5e4a261c31e7f43c47cbd72978e2ce
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
      containers:
        - name: ai-main
          image: registry-vpc.cn-shanghai.aliyuncs.com/topthink/ai:IMAGE_TAG
          imagePullPolicy: Always
          env:
            - name: PHP_APP_TOKEN
              value: "a20cde8268ef4a54d1b369ee973109f8"
            - name: PHP_DB_HOST
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: host
            - name: PHP_DB_USER
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: user
            - name: PHP_DB_PASS
              valueFrom:
                secretKeyRef:
                  name: db-config
                  key: password
            - name: PHP_REDIS_HOST
              value: redis-master.thirdparty
            - name: PHP_CLOUD_ENABLE
              value: "true"
            - name: PHP_CLOUD_CLIENT_ID
              value: a64bedb71f5a389870a64232ae6d74b0
            - name: PHP_CLOUD_CLIENT_SECRET
              value: 9c5e4a261c31e7f43c47cbd72978e2ce
            - name: PHP_CLOUD_RPC_HOST
              value: core-svc
            - name: PHP_API_HOST
              value: api-svc
            - name: PHP_TRACING_TYPE
              value: topthink-ai
            - name: PHP_ZIPKIN_ENDPOINT
              value: http://tracing-analysis-dc-sh-internal.aliyuncs.com/adapt_hvfmcpk6dx@82da6cbc6ee1477_hvfmcpk6dx@53df7ad2afe8301/api/v2/spans
            - name: aliyun_logs_ai
              value: /opt/htdocs/runtime/log/*.log
            - name: aliyun_logs_ai_project
              value: topthink
          resources:
            requests:
              cpu: 250m
              memory: 512Mi
          ports:
            - name: http
              containerPort: 80
              protocol: TCP
            - name: rpc
              containerPort: 9000
              protocol: TCP
          volumeMounts:
            - name: volume-log
              mountPath: /opt/htdocs/runtime/log/
            - name: volume-storage
              mountPath: /opt/htdocs/storage/
              subPath: ./ai
          readinessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          startupProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
          livenessProbe:
            failureThreshold: 3
            initialDelaySeconds: 15
            periodSeconds: 10
            successThreshold: 1
            tcpSocket:
              port: 8080
            timeoutSeconds: 1
      volumes:
        - name: volume-log
          emptyDir: { }
        - name: volume-storage
          persistentVolumeClaim:
            claimName: topthink-pvc
      serviceAccountName: topthink
---
apiVersion: v1
kind: Service
metadata:
  name: ai-svc
  namespace: topthink
spec:
  ports:
    - name: http
      port: 80
      protocol: TCP
      targetPort: 80
    - name: rpc
      port: 9000
      protocol: TCP
      targetPort: 9000
  selector:
    app: ai
  type: ClusterIP
---
apiVersion: v1
kind: ServiceAccount
metadata:
  name: topthink
  namespace: topthink
---
apiVersion: rbac.authorization.k8s.io/v1
kind: ClusterRoleBinding
metadata:
  name: topthink
  namespace: topthink
roleRef:
  apiGroup: rbac.authorization.k8s.io
  kind: ClusterRole
  name: cluster-admin
subjects:
  - kind: ServiceAccount
    name: topthink
    namespace: topthink
