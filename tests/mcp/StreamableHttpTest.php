<?php

namespace tests\mcp;

use app\lib\llm\mcp\StreamableHttp;
use app\lib\llm\mcp\Toolkit;
use tests\TestCase;

class StreamableHttpTest extends TestCase
{
    public function testStreamableHttpTransport()
    {
        // 这是一个基本的测试，验证 StreamableHttp 类可以正确实例化
        $this->assertTrue(class_exists(StreamableHttp::class));
        $this->assertTrue(method_exists(StreamableHttp::class, 'connect'));
        $this->assertTrue(method_exists(StreamableHttp::class, 'listTools'));
        $this->assertTrue(method_exists(StreamableHttp::class, 'callTool'));
    }

    public function testStreamableHttpExtendsTransport()
    {
        // 测试 StreamableHttp 正确继承了 Transport 抽象类
        $reflection = new \ReflectionClass(StreamableHttp::class);
        $this->assertEquals('app\lib\llm\mcp\Transport', $reflection->getParentClass()->getName());
    }

    public function testToolkitWithStreamableHttp()
    {
        // 测试 Toolkit 可以使用 streamable-http transport
        $toolkit = new Toolkit('http://example.com/mcp', 'streamable-http');
        $this->assertInstanceOf(Toolkit::class, $toolkit);
    }

    public function testToolkitWithHttpTransport()
    {
        // 测试 Toolkit 可以使用 http transport (别名)
        $toolkit = new Toolkit('http://example.com/mcp', 'http');
        $this->assertInstanceOf(Toolkit::class, $toolkit);
    }

    public function testTransportAbstractClass()
    {
        // 测试 Transport 是抽象类
        $reflection = new \ReflectionClass('app\lib\llm\mcp\Transport');
        $this->assertTrue($reflection->isAbstract());
        
        // 测试包含公共方法
        $this->assertTrue($reflection->hasMethod('getMessages'));
        $this->assertTrue($reflection->hasMethod('getNextId'));
        $this->assertTrue($reflection->hasMethod('getSessionId'));
        $this->assertTrue($reflection->hasMethod('setSessionId'));
    }
}
