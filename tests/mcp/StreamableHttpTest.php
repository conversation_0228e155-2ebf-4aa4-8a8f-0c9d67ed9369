<?php

namespace tests\mcp;

use app\lib\llm\mcp\StreamableHttp;
use app\lib\llm\mcp\Toolkit;
use tests\TestCase;

class StreamableHttpTest extends TestCase
{
    public function testStreamableHttpTransport()
    {
        // 这是一个基本的测试，验证 StreamableHttp 类可以正确实例化
        $this->assertTrue(class_exists(StreamableHttp::class));
        $this->assertTrue(method_exists(StreamableHttp::class, 'connect'));
        $this->assertTrue(method_exists(StreamableHttp::class, 'listTools'));
        $this->assertTrue(method_exists(StreamableHttp::class, 'callTool'));
    }

    public function testToolkitWithStreamableHttp()
    {
        // 测试 Toolkit 可以使用 streamable-http transport
        $toolkit = new Toolkit('http://example.com/mcp', 'streamable-http');
        $this->assertInstanceOf(Toolkit::class, $toolkit);
    }

    public function testToolkitWithHttpTransport()
    {
        // 测试 Toolkit 可以使用 http transport (别名)
        $toolkit = new Toolkit('http://example.com/mcp', 'http');
        $this->assertInstanceOf(Toolkit::class, $toolkit);
    }
}
