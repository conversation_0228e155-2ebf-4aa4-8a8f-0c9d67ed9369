<?php

namespace tests;

use ArrayAccess;
use think\helper\Arr;

abstract class TestCase extends \PHPUnit\Framework\TestCase
{
    protected function assertArrayHasKeys(mixed $key, array|ArrayAccess $array)
    {
        $this->assertTrue(Arr::has($array, $key));
    }

    protected function assertArrayNotHasKeys(mixed $key, array|ArrayAccess $array)
    {
        $this->assertFalse(Arr::has($array, $key));
    }
}
