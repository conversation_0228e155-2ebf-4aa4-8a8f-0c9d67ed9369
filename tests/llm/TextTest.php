<?php

namespace tests\llm;

use PHPUnit\Framework\Attributes\DataProvider;
use think\helper\Arr;

abstract class TextTest extends TestCase
{
    protected static $cases = [
        'embedding' => [],
        'rerank'    => [],
    ];

    private static function getModels($case)
    {
        $models = Arr::get(static::$cases, $case, []);

        $combinations = [];

        foreach ($models as $model) {
            $combinations[$model] = [$model];
        }

        if (empty($combinations)) {
            return [[]];
        }

        return $combinations;
    }

    public static function embeddingModels()
    {
        return self::getModels('embedding');
    }

    public static function rerankModels()
    {
        return self::getModels('rerank');
    }

    #[DataProvider('embeddingModels')]
    public function testEmbedding($model)
    {
        $length = static::$models[$model]['length'] ?? 0;

        $result = $this->llm->text()->embedding([
            'model' => $model,
            'input' => [
                'bb',
                'aa',
            ],
        ]);

        $this->assertArrayHasKeys('embeddings', $result);
        $this->assertCount(2, $result['embeddings']);
        $this->assertCount($length, $result['embeddings'][0]);

        $result = $this->llm->text()->embedding([
            'model' => $model,
            'input' => 'aaa',
        ]);

        $this->assertArrayHasKeys('embeddings', $result);
        $this->assertCount($length, $result['embeddings']);
    }

    #[DataProvider('rerankModels')]
    public function testRerank($model)
    {
        $result = $this->llm->text()->rerank([
            'model'     => $model,
            'query'     => '什么是文本排序模型',
            'documents' => [
                '文本排序模型广泛用于搜索引擎和推荐系统中，它们根据文本相关性对候选文本进行排序',
                '量子计算是计算科学的一个前沿领域',
                '预训练语言模型的发展给文本排序模型带来了新的进展',
            ],
        ]);

        $this->assertArrayHasKeys(['documents'], $result);
        $this->assertCount(3, $result['documents']);
    }
}
