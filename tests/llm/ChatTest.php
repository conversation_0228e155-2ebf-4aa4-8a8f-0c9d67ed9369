<?php

namespace tests\llm;

use PHPUnit\Framework\Attributes\DataProvider;
use think\helper\Arr;

abstract class ChatTest extends TestCase
{
    protected static $cases = [
        'base'      => [],
        'tools'     => [],
        'reasoning' => [],
    ];

    private static function getModels($case)
    {
        $models = Arr::get(static::$cases, $case, []);

        $streams = [true, false];

        $combinations = [];

        foreach ($models as $key => $model) {
            if (is_numeric($key)) {
                foreach ($streams as $stream) {
                    $name = "{$model}:" . ($stream ? 'true' : 'false');

                    $combinations[$name] = [$model, $stream];
                }
            } else {
                $combinations[$key] = [$key, $model];
            }
        }

        if (empty($combinations)) {
            return [['skip']];
        }

        return $combinations;
    }

    public static function baseModels()
    {
        return self::getModels('base');
    }

    public static function toolModels()
    {
        return self::getModels('tools');
    }

    public static function reasoningModels()
    {
        return self::getModels('reasoning');
    }

    #[DataProvider('baseModels')]
    public function testCompletions($model, $stream)
    {
        $result = $this->llm->chat()->completions([
            'model'    => $model,
            'messages' => [
                [
                    'role'    => 'user',
                    'content' => 'hi',
                ],
            ],
            'stream'   => $stream,
        ]);

        $result->rewind();
        if ($stream) {
            foreach ($result as $event) {
                $this->assertArrayHasKeys('finish_reason', $event);
                $reason = $event['finish_reason'];
                if ($reason === 'stop') {
                    $this->assertArrayHasKeys('usage', $event);
                } else {
                    $this->assertArrayHasKeys('delta', $event);
                }
            }
        } else {
            $this->assertFalse($result->valid());
            $return = $result->getReturn();
            $this->assertArrayHasKeys(['usage', 'finish_reason', 'message'], $return);
        }
    }

    #[DataProvider('toolModels')]
    public function testTools($model, $stream)
    {
        $tools = [
            [
                'type'     => 'function',
                'function' => [
                    'name'        => 'query_train_info',
                    'description' => '根据用户提供的信息查询火车时刻',
                    'parameters'  => [
                        'type'       => 'object',
                        'properties' => [
                            'departure'   => [
                                'type'        => 'string',
                                'description' => '出发城市或车站',
                            ],
                            'destination' => [
                                'type'        => 'string',
                                'description' => '目的地城市或车站',
                            ],
                            'date'        => [
                                'type'        => 'string',
                                'description' => '要查询的火车日期',
                            ],
                        ],
                        'required'   => ['departure', 'destination', 'date'],
                    ],
                ],
            ],
        ];

        $messages = [
            [
                'role'    => 'user',
                'content' => '你能帮我查一下2024年1月1日从北京南站到上海的火车票吗？',
            ],
        ];

        $result = $this->llm->chat()->completions([
            'model'    => $model,
            'messages' => $messages,
            'tools'    => $tools,
            'stream'   => $stream,
        ]);

        $result->rewind();
        if ($stream) {
            $tokens = 0;
            $calls  = [];

            foreach ($result as $event) {
                $this->assertArrayHasKeys('finish_reason', $event);
                $reason = $event['finish_reason'];
                if ($reason === 'stop' || $reason === 'tool_calls') {
                    $this->assertArrayHasKeys(['usage.total_tokens'], $event);
                    $tokens = $event['usage']['total_tokens'];
                } else {
                    $this->assertArrayHasKeys(['delta'], $event);
                    if (!empty($event['delta']['tool_calls'])) {
                        $calls += $event['delta']['tool_calls'];
                    }
                }
            }
        } else {
            $this->assertFalse($result->valid());
            $return = $result->getReturn();

            $this->assertArrayHasKeys(['usage.total_tokens', 'finish_reason', 'message', 'message.tool_calls'], $return);

            $tokens = $return['usage']['total_tokens'];
            $calls  = $return['message']['tool_calls'];
        }

        $this->assertGreaterThan(0, $tokens);
        $this->assertEquals(1, count($calls));
        $arguments = json_decode($calls[0]['function']['arguments'], true);
        $this->assertArrayHasKey('departure', $arguments);
        $this->assertArrayHasKey('destination', $arguments);
        $this->assertArrayHasKey('date', $arguments);
    }

    #[DataProvider('toolModels')]
    public function testLoopTools($model, $stream)
    {
        $tools = [
            [
                'type'     => 'function',
                'function' => [
                    'name'        => 'get_weather',
                    'description' => '获取近期某一地点的天气情况',
                    'parameters'  => [
                        'type'       => 'object',
                        'properties' => [
                            'location' => [
                                'type'        => 'string',
                                'description' => '某一个城市，比如北京、上海',
                            ],
                        ],
                        'required'   => ['location'],
                    ],
                ],
            ],
        ];

        $messages = [
            [
                'role'    => 'user',
                'content' => '广州天气怎么样',
            ],
            [
                'role'       => 'assistant',
                'content'    => '',
                'tool_calls' => [
                    [
                        'id'       => 'call_0',
                        'type'     => 'function',
                        'function' => [
                            'name'      => 'get_weather',
                            'arguments' => '{"location": "广州"}',
                        ],
                    ],
                ],
            ],
            [
                'role'         => 'tool',
                'tool_call_id' => 'call_0',
                'content'      => '多云，28~37℃，无持续风向<3级，空气质量优',
            ],
        ];

        $result = $this->llm->chat()->completions([
            'model'    => $model,
            'messages' => $messages,
            'tools'    => $tools,
            'stream'   => $stream,
        ]);

        $result->rewind();
        $content = '';
        if ($stream) {
            $tokens = 0;

            foreach ($result as $event) {
                $this->assertArrayHasKeys('finish_reason', $event);
                $reason = $event['finish_reason'];
                if ($reason === 'stop') {
                    $this->assertArrayHasKeys(['usage.total_tokens'], $event);
                    $tokens = $event['usage']['total_tokens'];
                } else {
                    $this->assertArrayHasKeys(['delta'], $event);
                    $this->assertArrayNotHasKeys(['delta.tool_calls'], $event);
                }
                $content .= $event['delta']['content'] ?? '';
            }
        } else {
            $this->assertFalse($result->valid());
            $return = $result->getReturn();

            $this->assertArrayHasKeys(['usage.total_tokens', 'finish_reason', 'message.content'], $return);
            $this->assertArrayNotHasKeys(['message.tool_calls'], $return);

            $tokens  = $return['usage']['total_tokens'];
            $content = $return['message']['content'];
        }
        $this->assertGreaterThan(0, $tokens);
        $this->assertStringContainsString('多云', $content);
    }

    #[DataProvider('reasoningModels')]
    public function testReasoning($model, $stream)
    {
        $result = $this->llm->chat()->completions([
            'model'    => $model,
            'messages' => [
                [
                    'role'    => 'user',
                    'content' => '1+1等于几？',
                ],
            ],
            'thinking' => 'enabled',
            'stream'   => $stream,
        ]);

        $result->rewind();
        $reasoning = '';
        $content   = '';
        if ($stream) {
            foreach ($result as $event) {
                $reasoning .= $event['delta']['reasoning'] ?? '';
                $content   .= $event['delta']['content'] ?? '';
                echo $event['delta']['reasoning'] ?? '';
                echo $event['delta']['content'] ?? '';
                ob_flush();
            }
        } else {
            $return    = $result->getReturn();
            $reasoning = $return['message']['reasoning'] ?? '';
            $content   = $return['message']['content'] ?? '';
        }

        dump($reasoning);
        dump($content);
        $this->assertNotEmpty($reasoning);
        $this->assertNotEmpty($content);
    }

}
