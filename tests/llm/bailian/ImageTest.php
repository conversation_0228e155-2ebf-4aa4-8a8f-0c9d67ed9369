<?php

namespace tests\llm\bailian;

use tests\llm\TestCase;

class ImageTest extends TestCase
{
    use WithEnv;

    public function testEdit()
    {
        $result = $this->llm->image()->edit([
            'model'  => 'wanx-v2',
            'prompt' => '给女孩加个墨镜',
            // 'image'  => 'data:image/png;base64,' . base64_encode(file_get_contents(__DIR__ . '/../../assets/img.png')),
            'image'  => 'https://chat.topthink.com/uploads/images/20240906/73bb3af167fe20964bd2d58446a2b780.png',
        ]);

        dump($result);
    }
}
