<?php

namespace tests\llm\volcengine;

use tests\llm\TestCase;

class MusicTest extends TestCase
{
    use WithEnv;

    public function testSong()
    {
        $result = $this->llm->music()->song([
            'model'  => 'volcengine',
            'prompt' => '关于爱情的伤感的歌',
            'duration' => 240,
        ]);

        dump($result);
    }

    public function testBgm()
    {
        $result = $this->llm->music()->bgm([
            'model'  => 'volcengine',
            'prompt' => '关于星空的背景纯音乐',
        ]);

        dump($result);
    }

    public function testQuery()
    {
        $result = $this->llm->music()->query([
            'model' => 'volcengine',
            'id'    => '202507365797580898041857',
        ]);

        dump($result);
    }
}
