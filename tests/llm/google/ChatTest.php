<?php

namespace tests\llm\google;

class ChatTest extends \tests\llm\ChatTest
{
    protected static $cases = [
        'base'      => ['gemini-2.5-pro', 'gemini-2.5-flash'],
        'tools'     => ['gemini-2.5-flash'],
        'reasoning' => ['gemini-2.5-pro'],
    ];

    protected static $models = [
        'gemini-2.5-flash' => [
            'thinking' => ['is' => true, 'closable' => true],
        ],
        'gemini-2.5-pro'   => [
            'thinking' => ['is' => true, 'closable' => true],
        ],
    ];

    protected function getAuth()
    {
        return env('GOOGLE_KEY');
    }

    public function getBaseUri()
    {
        return env('GOOGLE_BASE_URI');
    }

}
