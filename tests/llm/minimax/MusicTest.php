<?php

namespace tests\llm\minimax;

use tests\llm\TestCase;

class MusicTest extends TestCase
{
    use WithEnv;

    public function testSong()
    {
        $result = $this->llm->music()->song([
            'model'  => 'music-1.5',
            'prompt' => '关于爱情的伤感的歌 民谣风格',
            'lyrics' => <<<EOT
[intro]  
鼓声阵阵响起，烟花映红夜空  

[verse]  
红灯高挂映春风，巷陌洋溢笑声浓  
孩童手捧糖果甜，长街舞狮跃龙腾  
家人围坐话团圆，香炉袅袅绕心间  
老树新芽盼未来，千家万户庆盛年  

[inst]  

[chorus]  
新春到，喜气绕，万象更新共欢跳  
爆竹声声辞旧岁，朝霞映照好兆  
团圆饭，笑颜绕，幸福在每个微笑  
共贺这美好时刻，心手相牵到天涯  

[bridge]  
灯谜照亮夜色深，花市人潮涌动心  
红绸舞动吉祥影，岁月静好在此间  

[chorus]  
新春到，喜气绕，万象更新共欢跳  
爆竹声声辞旧岁，朝霞映照好兆  
团圆饭，笑颜绕，幸福在每个微笑  
共贺这美好时刻，心手相牵到天涯  

[outro]  
烟花渐散归宁静，春光无限暖人心
EOT
            ,
        ]);

        dump($result);
    }
}
