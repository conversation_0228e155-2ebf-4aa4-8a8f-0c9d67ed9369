<?php

namespace tests\llm\openai;

use tests\llm\TestCase;

class ImageTest extends TestCase
{
    use WithEnv;

    public function testGenerations()
    {
        $result = $this->llm->image()->generations([
            'model'  => 'gpt-4o-image',
            'prompt' => '制作一张vlog视频封面。马卡龙配色，美女旅游照片+色块的拼贴画风格，主文案是“威海旅游vlog”，副文案是“特种兵一日游 被低估的旅游城市”，海报主体是一个穿着短裙、梳双马尾的少女，人物白色描边',
        ]);
        dump($result);
        $this->assertArrayHasKey('images', $result);
        $this->assertArrayHasKey('usage', $result);
        $this->assertNotEmpty($result['images']);
        $this->assertArrayHasKey('url', $result['images'][0]);
    }
}
