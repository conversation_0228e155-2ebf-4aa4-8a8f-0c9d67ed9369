<?php

namespace tests\llm\openai;

class TextTest extends \tests\llm\TextTest
{
    use WithEnv;

    protected static $cases = [
        'embedding' => ['text-embedding-3-large', 'text-embedding-3-small', 'text-embedding-ada-002'],
        'rerank'    => [],
    ];

    protected static $models = [
        'text-embedding-3-large' => [
            'length' => 3072,
        ],
        'text-embedding-3-small' => [
            'length' => 1536,
        ],
        'text-embedding-ada-002' => [
            'length' => 1536,
        ],
    ];

}
