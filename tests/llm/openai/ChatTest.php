<?php

namespace tests\llm\openai;

class ChatTest extends \tests\llm\ChatTest
{
    use WithEnv;

    protected static $cases = [
        'base'      => ['gpt-3.5-turbo', 'gpt-4o-mini'],
        'tools'     => ['gpt-3.5-turbo'],
        'reasoning' => [],
    ];

    public function testChat()
    {
        $result = $this->llm->chat()->completions([
            'model'    => 'gpt-4.1',
            'messages' => [
                [
                    'role'    => 'user',
                    'content' => '上海怎么去南京',
                ],
            ],
            'stream'   => true,
        ]);

        $result->rewind();
        foreach ($result as $event) {
            $this->assertArrayHasKeys('finish_reason', $event);
            $reason = $event['finish_reason'];
            if ($reason === 'stop') {
                $this->assertArrayHasKeys('usage', $event);
            } else {
                $this->assertArrayHasKeys('delta', $event);
            }
        }
    }

}
