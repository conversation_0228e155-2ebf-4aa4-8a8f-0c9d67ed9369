server {
    listen  80;

    error_log /dev/stderr warn;
    access_log /dev/stdout main;
    client_max_body_size 20m;

    location = /favicon.ico {
         root /opt/htdocs/asset/dist/asset;
         try_files $uri =404;
         expires 10d;
    }

    location /uploads/ {
        root  /opt/htdocs/storage;
        try_files $uri =404;
        expires 10d;
    }

    location = /admin {
        rewrite ^ /admin/index.html last;
    }

    location ~ ^/admin/api/ {
        try_files /dev/null @swoole;
    }

    location ~ ^/admin/(.*) {
        root /opt/htdocs/asset/dist;
        try_files /$1 /index.html =404;
    }

    location / {
        try_files /dev/null @swoole;
    }

    location @swoole {
        proxy_pass http://127.0.0.1:8080;
        proxy_http_version 1.1;
        proxy_connect_timeout 300;
        proxy_send_timeout 600;
        proxy_read_timeout 600;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header Host $http_host;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
    }
}
