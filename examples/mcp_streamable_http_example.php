<?php

require_once __DIR__ . '/../vendor/autoload.php';

use app\lib\llm\mcp\StreamableHttp;
use app\lib\llm\mcp\Toolkit;

// 示例 1: 直接使用 StreamableHttp
try {
    echo "=== 直接使用 StreamableHttp ===\n";
    
    // 连接到 MCP 服务器
    $client = StreamableHttp::connect('http://localhost:3000/mcp');
    
    // 列出可用工具
    $tools = $client->listTools();
    echo "可用工具: " . json_encode($tools, JSON_UNESCAPED_UNICODE) . "\n";
    
    // 调用工具
    if (!empty($tools)) {
        $toolName = $tools[0]['name'];
        $result = $client->callTool($toolName, ['param1' => 'value1']);
        echo "工具调用结果: " . json_encode($result, JSON_UNESCAPED_UNICODE) . "\n";
    }
    
    echo "StreamableHttp 连接完成\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n";

// 示例 2: 通过 Toolkit 使用 StreamableHttp
try {
    echo "=== 通过 Toolkit 使用 StreamableHttp ===\n";
    
    // 创建 Toolkit 实例，使用 streamable-http transport
    $toolkit = new Toolkit('http://localhost:3000/mcp', 'streamable-http');
    
    // 获取工具列表
    $tools = $toolkit->getTools();
    echo "通过 Toolkit 获取的工具数量: " . count($tools) . "\n";
    
    foreach ($tools as $tool) {
        echo "工具: " . $tool->getName() . " - " . $tool->getDescription() . "\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n";

// 示例 3: 使用 http transport (别名)
try {
    echo "=== 使用 http transport (别名) ===\n";
    
    // 创建 Toolkit 实例，使用 http transport (这是 streamable-http 的别名)
    $toolkit = new Toolkit('http://localhost:3000/mcp', 'http');
    
    echo "成功创建使用 http transport 的 Toolkit\n";
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}

echo "\n=== 示例完成 ===\n";

// 示例 4: 展示 StreamableHttp 的优势
echo "=== StreamableHttp 的特点 ===\n";
echo "1. 无需 Channel 和协程，简化了实现\n";
echo "2. 直接处理 HTTP 响应，支持 JSON 和 SSE 两种模式\n";
echo "3. 自动处理会话管理 (Session ID)\n";
echo "4. 遵循 MCP 2025-06-18 规范\n";
echo "5. 支持超时控制和错误处理\n";
