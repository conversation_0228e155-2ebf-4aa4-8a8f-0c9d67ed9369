import { createRoot } from 'react-dom/client';
import './scss/app.scss';
import { createApplication, Form, request, RequestButton, useLocation, useNavigate } from '@topthink/common';
import routes from '@/routes';
import { useEffect, useRef } from 'react';
import ManifestProvider, { useManifest } from './components/manifest-provider';

const container = document.getElementById('app');

if (container) {
    const root = createRoot(container);
    const App = createApplication({
        basename: '/admin',
        baseURL: '/admin/api',
        routes,
        RootComponent: ManifestProvider,
        async userResolver() {
            const user = await request('/auth/current');
            user.avatar = require('./images/avatar.png').default;
            return user;
        },
        onAuthorize: async (token) => {
            const result = await request({
                url: '/cloud/login',
                method: 'POST',
                data: { token }
            });
            return result.token;
        },
        onLogin: ({ onAuthorize, onLogined }) => {
            let { cloud } = useManifest();
            if (cloud) {
                const ref = useRef<HTMLButtonElement>(null);
                const { state } = useLocation();
                useEffect(() => {
                    if (state?.from !== 'logout') {
                        ref.current?.click();
                    }
                }, []);

                return <div className={'d-grid'}>
                    <RequestButton
                        ref={ref}
                        variant={'outline-primary'}
                        url={`/cloud/login`}
                        onSuccess={({ url }) => onAuthorize(url)}
                    >使用 顶想云 登录</RequestButton>
                </div>;
            } else {
                const navigate = useNavigate();
                return <>
                    <h2 className='text-center mb-5'>ThinkAI</h2>
                    <Form
                        action={'/auth/login'}
                        method={'post'}
                        schema={{
                            type: 'object',
                            properties: {
                                username: {
                                    type: 'string',
                                    title: '用户名'
                                },
                                password: {
                                    type: 'string',
                                    title: '密码',
                                    format: 'password'
                                }
                            }
                        }}
                        onSuccess={({ token }) => {
                            const url = onLogined(token);
                            navigate(url);
                        }}
                        submitText={'登录'}
                        children={({ submit }) => {
                            return <div className='col-12 d-grid'>
                                {submit}
                            </div>;
                        }}
                    />
                </>;
            }
        }
    });

    root.render(<App />);
}
