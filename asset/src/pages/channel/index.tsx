import type { FormSchema, FormUiSchema } from '@topthink/common';
import { Content, ModalForm, RequestButton, Space, Table } from '@topthink/common';

export const Component = () => {
    const schema: FormSchema = {
        type: 'object',
        properties: {
            name: {
                type: 'string',
                title: '名称'
            },
            driver: {
                type: 'string',
                title: '驱动',
            },
            auth: {
                type: 'array',
                title: '认证信息',
                items: {
                    type: 'string',
                }
            },
            base_uri: {
                type: 'string',
                title: '接口地址',
                description: '可选，留空则使用默认地址'
            },
        },
        dependencies: {
            driver: {
                oneOf: [
                    {
                        properties: {
                            driver: {
                                enum: ['hunyuan', 'meitu', 'volcengine']
                            },
                            auth: {
                                items: {
                                    type: 'object',
                                    properties: {
                                        access_key: {
                                            type: 'string',
                                            title: 'Access Key'
                                        },
                                        secret_key: {
                                            type: 'string',
                                            title: 'Secret Key'
                                        }
                                    }
                                }
                            }
                        }
                    },
                    {
                        properties: {
                            driver: {
                                enum: ['spark']
                            },
                            auth: {
                                items: {
                                    type: 'object',
                                    properties: {
                                        app_id: {
                                            type: 'string',
                                            title: 'App ID'
                                        },
                                        api_key: {
                                            type: 'string',
                                            title: 'API Key'
                                        },
                                        api_secret: {
                                            type: 'string',
                                            title: 'API Secret'
                                        },
                                        api_password: {
                                            type: 'string',
                                            title: 'API Password'
                                        }
                                    }
                                }
                            }
                        }
                    },
                ]
            }
        }
    };

    const uiSchema: FormUiSchema = {
        name: {
            'ui:autofocus': true,
            'ui:placeholder': '输入渠道名称'
        },
        driver: {
            'ui:widget': 'typeahead',
            'ui:options': {
                endpoint: '/channel/drivers',
                placeholder: '选择驱动类',
                async: false
            }
        },
        auth: {
            'ui:options': {
                orderable: false,
            },
            'items': {
                'ui:placeholder': '输入API Key',
                'ui:label': false,
                access_key: {
                    'ui:label': false,
                    'ui:col': 6,
                    'ui:placeholder': 'Access Key'
                },
                secret_key: {
                    'ui:label': false,
                    'ui:col': 6,
                    'ui:placeholder': 'Secret Key'
                },
                app_id: {
                    'ui:label': false,
                    'ui:col': 3,
                    'ui:placeholder': 'App ID'
                },
                api_key: {
                    'ui:label': false,
                    'ui:col': 3,
                    'ui:placeholder': 'API Key'
                },
                api_secret: {
                    'ui:label': false,
                    'ui:col': 3,
                    'ui:placeholder': 'API Secret'
                },
                api_password: {
                    'ui:label': false,
                    'ui:col': 3,
                    'ui:placeholder': 'API Password'
                }
            }
        },
        base_uri: {
            'ui:placeholder': '输入接口地址'
        }
    };

    return <Content>
        <Table
            toolBarRender={(action) => {
                return <ModalForm
                    text={'添加渠道'}
                    method={'post'}
                    modalProps={{ size: 'lg' }}
                    action={`/channel`}
                    schema={schema}
                    uiSchema={uiSchema}
                    onSuccess={action.reload}
                />;
            }}
            source={`/channel`}
            columns={[
                {
                    dataIndex: 'name',
                    title: '渠道名称',
                },
                {
                    dataIndex: 'driver',
                    title: '驱动类',
                    width: 250,
                },
                {
                    key: 'action',
                    align: 'right',
                    width: 120,
                    render({ record, action }) {
                        return <Space>
                            <ModalForm
                                text={'编辑'}
                                method={'put'}
                                modalProps={{ size: 'lg' }}
                                action={`/channel/${record.id}`}
                                schema={schema}
                                uiSchema={uiSchema}
                                formData={record}
                                onSuccess={action.reload}
                            />
                            <RequestButton
                                method={'delete'}
                                url={`/channel/${record.id}`}
                                confirm={'确定删除吗？'}
                                onSuccess={action.reload}
                            >删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
