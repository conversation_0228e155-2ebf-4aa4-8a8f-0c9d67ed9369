import {
    Content,
    FormSchema,
    FormUiSchema,
    ModalForm,
    RequestButton,
    Space,
    Table, Toast,
    useClipboard
} from '@topthink/common';

export const Component = () => {
    const schema: FormSchema = {
        type: 'object',
        required: ['name'],
        properties: {
            name: {
                type: 'string',
                title: '名称'
            }
        }
    };

    const uiSchema: FormUiSchema = {
        name: {
            'ui:autofocus': true,
            'ui:placeholder': '请输入API Key名称'
        }
    };

    const { copy } = useClipboard();

    return <Content>
        <Table
            toolBarRender={(action) => {
                return <ModalForm
                    text={'创建API Key'}
                    method={'post'}
                    action={`/key`}
                    schema={schema}
                    uiSchema={uiSchema}
                    onSuccess={action.reload}
                />;
            }}
            source={`/key`}
            columns={[
                {
                    dataIndex: 'id',
                    title: 'ID',
                    width: 80,
                },
                {
                    dataIndex: 'name',
                    title: '名称',
                },
                {
                    dataIndex: 'value',
                    title: 'API Key',
                    width: 350,
                    render: ({ value }) => (
                        <div className='d-inline-flex align-items-center'>
                            <span className='font-mono text-xs'>{value}</span>
                            <button
                                className='btn btn-xs text-muted p-0 ms-1'
                                onClick={(e) => {
                                    e.stopPropagation();
                                    copy(value);
                                    Toast.success('复制成功');
                                }}
                                title='复制到剪贴板'
                                style={{ border: 'none', background: 'transparent' }}
                            >
                                <i className='bi bi-clipboard'></i>
                            </button>
                        </div>
                    ),
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 180,
                },
                {
                    dataIndex: 'last_time',
                    title: '最后使用时间',
                    width: 180,
                },
                {
                    key: 'action',
                    title: '操作',
                    align: 'right',
                    width: 120,
                    render({ record, action }) {
                        return <Space>
                            <RequestButton
                                method={'delete'}
                                url={`/key/${record.id}`}
                                confirm={'确定删除该API Key吗？'}
                                onSuccess={action.reload}
                            >删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};

export default Component;
