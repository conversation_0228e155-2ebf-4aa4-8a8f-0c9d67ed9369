import {
    FormSchema,
    FormUiSchema,
    ModalForm,
    request,
    RequestButton,
    Space,
    Table,
    Tooltip
} from '@topthink/common';
import { Badge, FormCheck } from 'react-bootstrap';
import { getChannelColor } from '@/utils/channel-color';

export const Component = () => {

    const schema: FormSchema = {
        type: 'object',
        properties: {
            channel_id: {
                type: 'number',
                title: '渠道'
            },
            label: {
                type: 'string',
                title: '名称'
            },
            description: {
                type: 'string',
                title: '描述'
            },
            code: {
                type: 'string',
                title: '标识',
                description: '我们对外提供的模型名'
            },
            version: {
                type: 'string',
                title: '版本',
                description: '实际厂商那边的模型名，留空则和标识一致'
            },
            factor: {
                type: 'object',
                title: '费率',
                description: '留空则表示不支持',
                properties: {
                    embedding: {
                        type: 'number',
                        title: '向量',
                    },
                    rerank: {
                        type: 'number',
                        title: '排序',
                    },
                }
            },
            params: {
                type: 'object',
                title: '其他参数',
                properties: {
                    size: {
                        type: 'string',
                        title: '向量维度'
                    }
                }
            },
            sort: {
                type: 'number',
                title: '排序',
                default: 0
            },
        },
    };

    const uiSchema: FormUiSchema = {
        channel_id: {
            'ui:widget': 'typeahead',
            'ui:options': {
                endpoint: '/model/channels',
                placeholder: '选择渠道',
                async: false
            }
        },
        description: {
            'ui:widget': 'textarea',
            'ui:options': {
                rows: 3
            }
        },
        code: {
            'ui:col': 6
        },
        version: {
            'ui:col': 6
        },
        factor: {
            embedding: {
                'ui:col': 6
            },
            rerank: {
                'ui:col': 6
            },
        }
    };

    return <Table
        toolBarRender={(action) => {
            return <ModalForm
                method='post'
                modalProps={{ size: 'lg' }}
                transformData={data => ({
                    type: 'text',
                    ...data,
                })}
                action={`/model`}
                text={'添加模型'}
                schema={schema}
                uiSchema={uiSchema}
                onSuccess={action.reload}
            />;
        }}
        source={`/model?type=text`}
        columns={[
            {
                dataIndex: 'label',
                title: '名称',
                render({ value, record }) {
                    return <Space>
                        <Badge bg={'custom'} style={{ backgroundColor: getChannelColor(record.channel_id) }}>{record.channel?.name || '未知'}</Badge>
                        {value}
                    </Space>;
                }
            },
            {
                dataIndex: 'code',
                title: '标识',
                render({ value, record }) {
                    return <>
                        {value}
                        {record.version && record.version !== record.code ?
                            <span className={'text-muted'}> ({record.version})</span> : ''}
                    </>;
                }
            },
            {
                dataIndex: 'factor',
                title: '费率',
                render({ value }) {
                    return <Space>
                        {value.embedding > 0 && <Tooltip placement={'top'} tooltip={`费率:${value.embedding}`}>
                            <Badge role={'button'} bg={'success'}>向量</Badge>
                        </Tooltip>}
                        {value.rerank > 0 && <Tooltip placement={'top'} tooltip={`费率:${value.rerank}`}>
                            <Badge role={'button'} bg={'success'}>排序</Badge>
                        </Tooltip>}
                    </Space>;
                }
            },
            {
                dataIndex: 'sort',
                title: '排序',
                width: 50,
                align: 'center'
            },
            {
                key: 'hide',
                title: '显示',
                width: 50,
                align: 'center',
                render({ record, action }) {
                    const status = 2;
                    return <FormCheck
                        checked={(record.status & status) === 0}
                        type={'switch'}
                        onChange={async (e) => {
                            await request({
                                method: 'post',
                                url: `/model/${record.id}/status`,
                                data: {
                                    status: e.target.checked ? record.status & ~status : record.status | status
                                }
                            });

                            action.reload(true);
                        }}
                    />;
                }
            },
            {
                key: 'action',
                title: '操作',
                align: 'right',
                width: 100,
                render({ record, action }) {
                    return <Space>
                        <ModalForm
                            text={'编辑'}
                            method='put'
                            modalProps={{ size: 'lg' }}
                            action={`/model/${record.id}`}
                            schema={schema}
                            uiSchema={uiSchema}
                            formData={record}
                            onSuccess={action.reload}
                        />
                        <RequestButton
                            confirm={'确定要删除吗？'}
                            url={`/model/${record.id}`}
                            method='delete'
                            onSuccess={action.reload}
                        >删除</RequestButton>
                    </Space>;
                }
            }
        ]}
    />;
};
