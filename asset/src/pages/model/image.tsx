import {
    FormSchema,
    FormUiSchema,
    ModalForm,
    request,
    RequestButton,
    Space,
    Table,
    Tooltip
} from '@topthink/common';
import { Badge, FormCheck } from 'react-bootstrap';
import { getChannelColor } from '@/utils/channel-color';

export const Component = () => {

    const schema: FormSchema = {
        type: 'object',
        properties: {
            channel_id: {
                type: 'number',
                title: '渠道'
            },
            label: {
                type: 'string',
                title: '名称'
            },
            description: {
                type: 'string',
                title: '描述'
            },
            code: {
                type: 'string',
                title: '标识',
                description: '我们对外提供的模型名'
            },
            version: {
                type: 'string',
                title: '版本',
                description: '实际厂商那边的模型名，留空则和标识一致'
            },
            factor: {
                type: 'object',
                title: '费率',
                description: '留空则表示不支持',
                properties: {
                    generations: {
                        type: 'number',
                        title: '文字作画',
                    },
                    inpainting: {
                        type: 'number',
                        title: '局部重绘',
                    },
                    outpainting: {
                        type: 'number',
                        title: '图片扩展',
                    },
                    upscale: {
                        type: 'number',
                        title: '高清修复',
                    },
                    poster: {
                        type: 'number',
                        title: '创意海报',
                    },
                    edit: {
                        type: 'number',
                        title: '指令编辑',
                    },
                }
            },
            sort: {
                type: 'number',
                title: '排序',
                default: 0
            },
        },
    };

    const uiSchema: FormUiSchema = {
        channel_id: {
            'ui:widget': 'typeahead',
            'ui:options': {
                endpoint: '/model/channels',
                placeholder: '选择渠道',
                async: false
            }
        },
        description: {
            'ui:widget': 'textarea',
            'ui:options': {
                rows: 3
            }
        },
        code: {
            'ui:col': 6
        },
        version: {
            'ui:col': 6
        },
        factor: {
            generations: {
                'ui:col': 4
            },
            inpainting: {
                'ui:col': 4
            },
            outpainting: {
                'ui:col': 4
            },
            upscale: {
                'ui:col': 4
            },
            poster: {
                'ui:col': 4
            },
            edit: {
                'ui:col': 4
            },
        }
    };

    return <Table
        toolBarRender={(action) => {
            return <ModalForm
                method='post'
                modalProps={{ size: 'lg' }}
                transformData={data => ({
                    type: 'image',
                    ...data,
                })}
                action={`/model`}
                text={'添加模型'}
                schema={schema}
                uiSchema={uiSchema}
                onSuccess={action.reload}
            />;
        }}
        source={`/model?type=image`}
        columns={[
            {
                dataIndex: 'label',
                title: '名称',
                render({ value, record }) {
                    return <Space>
                        <Badge bg={'custom'} style={{ backgroundColor: getChannelColor(record.channel_id) }}>{record.channel?.name || '未知'}</Badge>
                        {value}
                    </Space>;
                }
            },
            {
                dataIndex: 'code',
                title: '标识',
                render({ value, record }) {
                    return <>
                        {value}
                        {record.version && record.version !== record.code ?
                            <span className={'text-muted'}> ({record.version})</span> : ''}
                    </>;
                }
            },
            {
                dataIndex: 'factor',
                title: '费率',
                render({ value }) {
                    return <Space>
                        {value.generations && <Tooltip placement={'top'} tooltip={`费率:${value.generations}`}>
                            <Badge role={'button'} bg={'success'}>文字作画</Badge>
                        </Tooltip>}
                        {value.inpainting && <Tooltip placement={'top'} tooltip={`费率:${value.inpainting}`}>
                            <Badge role={'button'} bg={'success'}>局部重绘</Badge>
                        </Tooltip>}
                        {value.outpainting && <Tooltip placement={'top'} tooltip={`费率:${value.outpainting}`}>
                            <Badge role={'button'} bg={'success'}>图片扩展</Badge>
                        </Tooltip>}
                        {value.upscale && <Tooltip placement={'top'} tooltip={`费率:${value.upscale}`}>
                            <Badge role={'button'} bg={'success'}>高清修复</Badge>
                        </Tooltip>}
                        {value.poster && <Tooltip placement={'top'} tooltip={`费率:${value.poster}`}>
                            <Badge role={'button'} bg={'success'}>创意海报</Badge>
                        </Tooltip>}
                        {value.edit && <Tooltip placement={'top'} tooltip={`费率:${value.edit}`}>
                            <Badge role={'button'} bg={'success'}>指令编辑</Badge>
                        </Tooltip>}
                    </Space>;
                }
            },
            {
                dataIndex: 'sort',
                title: '排序',
                width: 50,
                align: 'center'
            },
            {
                key: 'hide',
                title: '显示',
                width: 50,
                align: 'center',
                render({ record, action }) {
                    const status = 2;
                    return <FormCheck
                        checked={(record.status & status) === 0}
                        type={'switch'}
                        onChange={async (e) => {
                            await request({
                                method: 'post',
                                url: `/model/${record.id}/status`,
                                data: {
                                    status: e.target.checked ? record.status & ~status : record.status | status
                                }
                            });

                            action.reload(true);
                        }}
                    />;
                }
            },
            {
                key: 'action',
                title: '操作',
                align: 'right',
                width: 100,
                render({ record, action }) {
                    return <Space>
                        <ModalForm
                            text={'编辑'}
                            method='put'
                            modalProps={{ size: 'lg' }}
                            action={`/model/${record.id}`}
                            schema={schema}
                            uiSchema={uiSchema}
                            formData={record}
                            onSuccess={action.reload}
                        />
                        <RequestButton
                            confirm={'确定要删除吗？'}
                            url={`/model/${record.id}`}
                            method='delete'
                            onSuccess={action.reload}
                        >删除</RequestButton>
                    </Space>;
                }
            }
        ]}
    />;
};
