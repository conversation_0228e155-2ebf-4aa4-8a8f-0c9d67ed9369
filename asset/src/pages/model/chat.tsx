import {
    FormSchema,
    FormUiSchema,
    ModalForm,
    request,
    RequestButton,
    Space,
    styled,
    Table,
    Tooltip
} from '@topthink/common';
import { Badge, FormCheck } from 'react-bootstrap';
import { getChannelColor } from '@/utils/channel-color';

export const Component = () => {
    const schema: FormSchema = {
        type: 'object',
        properties: {
            channel_id: {
                type: 'number',
                title: '渠道',
            },
            label: {
                type: 'string',
                title: '名称'
            },
            description: {
                type: 'string',
                title: '描述'
            },
            code: {
                type: 'string',
                title: '标识',
                description: '我们对外提供的模型名'
            },
            version: {
                type: 'string',
                title: '版本',
                description: '实际厂商那边的模型名，留空则和标识一致'
            },
            factor: {
                type: 'string',
                title: '费率',
                description: '区分输入输出时使用"/"隔开'
            },
            params: {
                type: 'object',
                title: '其他参数',
                properties: {
                    tool: {
                        type: 'boolean',
                        title: '工具调用'
                    },
                    vision: {
                        type: 'boolean',
                        title: '视觉'
                    },
                    context_tokens: {
                        type: 'number',
                        title: '上下文长度'
                    },
                    thinking: {
                        type: 'object',
                        title: '深度思考',
                        properties: {
                            is: {
                                type: 'boolean',
                                title: '是否支持'
                            },
                        },
                        dependencies: {
                            is: {
                                oneOf: [
                                    {
                                        properties: {
                                            is: {
                                                enum: [true]
                                            },
                                            closable: {
                                                type: 'boolean',
                                                title: '支持关闭'
                                            }
                                        },
                                        dependencies:{
                                            closable: {
                                                oneOf: [
                                                    {
                                                        properties: {
                                                            closable: {
                                                                enum: [true]
                                                            },
                                                            auto: {
                                                                type: 'boolean',
                                                                title: '支持自动'
                                                            }
                                                        }
                                                    }
                                                ]
                                            }
                                        }
                                    }
                                ]
                            }
                        }
                    },
                }
            },
            sort: {
                type: 'number',
                title: '排序',
                default: 0
            },
        },
    };

    const uiSchema: FormUiSchema = {
        channel_id: {
            'ui:widget': 'typeahead',
            'ui:options': {
                endpoint: '/model/channels',
                placeholder: '选择渠道',
                async: false
            }
        },
        description: {
            'ui:widget': 'textarea',
            'ui:options': {
                rows: 3
            }
        },
        code: {
            'ui:col': 6
        },
        version: {
            'ui:col': 6
        },
        params: {
            tool: {
                'ui:col': 6
            },
            vision: {
                'ui:col': 6
            },
            thinking: {
                is: {
                    'ui:col': 4
                },
                closable: {
                    'ui:col': 4
                },
                auto: {
                    'ui:col': 4
                }
            },
        }
    };

    return <Table
        toolBarRender={(action) => {
            return <ModalForm
                method='post'
                modalProps={{ size: 'lg' }}
                transformData={data => ({
                    type: 'chat',
                    ...data,
                })}
                action={`/model`}
                text={'添加模型'}
                omitExtraData
                schema={schema}
                uiSchema={uiSchema}
                onSuccess={action.reload}
            />;
        }}
        source={`/model?type=chat`}
        columns={[
            {
                dataIndex: 'label',
                title: '名称',
                render({ value, record }) {
                    return <Space size={4}>
                        <Badge bg={'custom'} style={{ backgroundColor: getChannelColor(record.channel_id) }}>{record.channel?.name || '未知'}</Badge>
                        {value}
                    </Space>;
                }
            },
            {
                dataIndex: 'params',
                title: '功能',
                render({ record }) {
                    return <Space size={4}>
                        {record.params?.tool && <Tooltip tooltip={'支持工具调用'}>
                            <Feature><i className='bi bi-tools' /></Feature>
                        </Tooltip>}
                        {record.params?.vision && <Tooltip tooltip={'支持视觉'}>
                            <Feature><i className='bi bi-eye' /></Feature>
                        </Tooltip>}
                        {record.params?.thinking?.is && <Tooltip tooltip={'支持推理'}>
                            <Feature><i className='bi bi-lightbulb' /></Feature>
                        </Tooltip>}
                    </Space>;
                }
            },
            {
                dataIndex: 'code',
                title: '标识',
                render({ value, record }) {
                    return <>
                        {value}
                        {record.version && record.version !== record.code ?
                            <span className={'text-muted'}> ({record.version})</span> : ''}
                    </>;
                }
            },
            {
                dataIndex: 'factor',
                title: '费率',
                width: 120,
            },
            {
                dataIndex: 'sort',
                title: '排序',
                width: 50,
                align: 'center'
            },
            {
                key: 'hide',
                title: '显示',
                width: 50,
                align: 'center',
                render({ record, action }) {
                    const status = 2;
                    return <FormCheck
                        checked={(record.status & status) === 0}
                        type={'switch'}
                        onChange={async (e) => {
                            await request({
                                method: 'post',
                                url: `/model/${record.id}/status`,
                                data: {
                                    status: e.target.checked ? record.status & ~status : record.status | status
                                }
                            });

                            action.reload(true);
                        }}
                    />;
                }
            },
            {
                key: 'action',
                title: '操作',
                align: 'right',
                width: 100,
                render({ record, action }) {
                    return <Space>
                        <ModalForm
                            text={'编辑'}
                            method='put'
                            modalProps={{ size: 'lg' }}
                            action={`/model/${record.id}`}
                            omitExtraData
                            schema={schema}
                            uiSchema={uiSchema}
                            formData={record}
                            onSuccess={action.reload}
                        />
                        <RequestButton
                            confirm={'确定要删除吗？'}
                            url={`/model/${record.id}`}
                            method='delete'
                            onSuccess={action.reload}
                        >删除</RequestButton>
                    </Space>;
                }
            }
        ]}
    />;
};

const Feature = styled.div`
    font-size: 10px;
    line-height: 1;
    background-color: hsla(0, 0%, 100%, .48);
    border: 1px solid var(--bs-border-color);
    border-radius: 5px;
    padding-left: .25rem;
    padding-right: .25rem;
    height: 18px;
    display: flex;
    align-items: center;
    justify-content: center;
    color: rgb(107 114 128);
`;
