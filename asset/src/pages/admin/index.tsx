import {
    Card,
    Content,
    FormSchema,
    FormUiSchema,
    ModalForm,
    RequestButton,
    Result,
    Space,
    Table
} from '@topthink/common';
import { useManifest } from '../../components/manifest-provider';

export const Component = () => {
    let { cloud } = useManifest();

    if (cloud) {
        return <Content>
            <Card>
                <Result status={'info'} title={'由顶想云托管'} />
            </Card>
        </Content>;
    }

    const schema: FormSchema = {
        type: 'object',
        required: ['username'],
        properties: {
            username: {
                type: 'string',
                title: '用户名'
            },
            password: {
                type: 'string',
                title: '密码'
            }
        }
    };

    const uiSchema: FormUiSchema = {
        username: {
            'ui:autofocus': true,
            'ui:placeholder': '请输入用户名'
        },
        password: {
            'ui:widget': 'password',
            'ui:placeholder': '请输入密码'
        }
    };

    return <Content>
        <Table
            toolBarRender={(action) => {
                return <ModalForm
                    text={'添加管理员'}
                    method={'post'}
                    action={`/admin`}
                    schema={schema}
                    uiSchema={uiSchema}
                    onSuccess={action.reload}
                />;
            }}
            source={`/admin`}
            columns={[
                {
                    dataIndex: 'id',
                    title: 'ID',
                    width: 80,
                },
                {
                    dataIndex: 'username',
                    title: '用户名',
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 180,
                },
                {
                    key: 'action',
                    title: '操作',
                    align: 'right',
                    width: 120,
                    render({ record, action }) {
                        return <Space>
                            <ModalForm
                                text={'编辑'}
                                method={'put'}
                                action={`/admin/${record.id}`}
                                schema={{
                                    ...schema,
                                    required: ['username']
                                }}
                                uiSchema={{
                                    ...uiSchema,
                                    password: {
                                        ...uiSchema.password,
                                        'ui:help': '不修改密码请留空'
                                    }
                                }}
                                formData={{
                                    username: record.username,
                                    password: ''
                                }}
                                onSuccess={action.reload}
                            />
                            {record.id > 1 && <RequestButton
                                method={'delete'}
                                url={`/admin/${record.id}`}
                                confirm={'确定删除该管理员吗？'}
                                onSuccess={action.reload}
                            >删除</RequestButton>}
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
