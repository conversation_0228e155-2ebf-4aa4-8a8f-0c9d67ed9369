import { Content, request, RequestButton, Space, Table } from '@topthink/common';
import { <PERSON>ge, FormCheck, Stack } from 'react-bootstrap';
import InfoForm from './info-form';
import ThinkConfig from './think-config';
import OpenConfig from './open-config';
import VisionConfig from './vision-config';
import ArtistConfig from './artist-config';

export const Component = () => {
    return <Content>
        <Table
            toolBarRender={(action) => {
                return <InfoForm
                    text={'添加插件'}
                    method={'post'}
                    action={`/plugin`}
                    onSuccess={action.reload}
                />;
            }}
            source={`/plugin`}
            columns={[
                {
                    dataIndex: 'name',
                    title: '名称',
                    render({ record }) {
                        let type = record.type;

                        const bg = type === 'OpenApi' ? 'info' : type === 'ThinkApi' ? 'primary' : 'secondary';

                        return <Stack direction={'horizontal'} gap={3}>
                            <img className={'rounded'} src={record.icon} width={30} height={30} />
                            <Stack gap={1}>
                                <span className='link-dark'>
                                    {record.title}
                                    <Badge bg={bg} className='ms-2'>
                                        {type}
                                    </Badge>
                                </span>
                                <span className='fs-7 text-muted'>{record.description || '暂无描述'}</span>
                            </Stack>
                        </Stack>;
                    }
                },
                {
                    dataIndex: 'name',
                    title: '标识',
                    width: 100,
                },
                {
                    dataIndex: 'create_time',
                    title: '创建时间',
                    width: 150,
                },
                {
                    dataIndex: 'sort',
                    title: '排序',
                    width: 50,
                    align: 'center'
                },
                {
                    key: 'status',
                    title: '状态',
                    width: 50,
                    align: 'center',
                    render({ record, action }) {
                        return <FormCheck
                            checked={record.status === 1}
                            type={'switch'}
                            onChange={async (e) => {
                                await request({
                                    method: 'post',
                                    url: `/plugin/${record.id}/status`,
                                    data: {
                                        status: e.target.checked ? 1 : 0
                                    }
                                });

                                action.reload(true);
                            }}
                        />;
                    }
                },
                {
                    key: 'action',
                    align: 'right',
                    width: 120,
                    render({ record, action }) {
                        return <Space>
                            {record.type === 'OpenApi' && <OpenConfig
                                plugin={record}
                                onSuccess={action.reload}
                            />}
                            {record.type === 'ThinkApi' && <ThinkConfig
                                plugin={record}
                                onSuccess={action.reload}
                            />}
                            {record.type === 'Vision' && <VisionConfig
                                plugin={record}
                                onSuccess={action.reload}
                            />}
                            {record.type === 'Artist' && <ArtistConfig
                                plugin={record}
                                onSuccess={action.reload}
                            />}
                            <InfoForm
                                text={'编辑'}
                                method={'put'}
                                action={`/plugin/${record.id}`}
                                onSuccess={action.reload}
                                formData={{
                                    ...record,
                                    name: record.real_name
                                }}
                            />
                            <RequestButton
                                method={'delete'}
                                url={`/plugin/${record.id}`}
                                confirm={'确定删除吗？'}
                                onSuccess={action.reload}
                            >删除</RequestButton>
                        </Space>;
                    }
                }
            ]}
        />
    </Content>;
};
