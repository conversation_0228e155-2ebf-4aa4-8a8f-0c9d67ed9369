import YamlEditor from '@/components/yaml-editor';
import { FormWidgetProps, ModalButton, ModalForm, useDebounce, useRequest } from '@topthink/common';
import { useEffect } from 'react';
import { Alert } from 'react-bootstrap';

export default function OpenConfig({ plugin, onSuccess }: { plugin: Plugin, onSuccess?: () => void }) {
    return <ModalForm
        text={'配置'}
        action={`/plugin/${plugin.id}/config`}
        modalProps={{
            header: 'OpenAPI配置',
            size: 'xl',
            scrollable: true
        }}
        formData={{
            auth: plugin.config?.auth,
            schema: plugin.config?.schema
        }}
        schema={{
            type: 'object',
            properties: {
                schema: {
                    type: 'string',
                    title: 'Schema'
                },
                auth: {
                    type: 'object',
                    title: '鉴权方式',
                    properties: {
                        type: {
                            type: 'string',
                            enum: ['none', 'apiKey', 'http'],
                            enumNames: ['无需鉴权', 'API Key', 'HTTP'],
                            default: 'none'
                        },
                    },
                    dependencies: {
                        type: {
                            oneOf: [
                                {
                                    properties: {
                                        type: {
                                            enum: ['none']
                                        }
                                    }
                                },
                                {
                                    properties: {
                                        type: {
                                            enum: ['apiKey']
                                        },
                                        in: {
                                            type: 'string',
                                            title: '位置',
                                            enum: ['header', 'query'],
                                            enumNames: ['Header', 'Query'],
                                            default: 'header'
                                        },
                                        name: {
                                            type: 'string',
                                            title: '名称',
                                        },
                                        key: {
                                            type: 'string',
                                            title: 'API Key',
                                        }
                                    }
                                },
                                {
                                    properties: {
                                        type: {
                                            enum: ['http']
                                        },
                                        scheme: {
                                            type: 'string',
                                            title: 'Schema',
                                            enum: ['bearer', 'basic'],
                                            enumNames: ['Bearer', 'Basic'],
                                            default: 'bearer'
                                        }
                                    },
                                    dependencies: {
                                        scheme: {
                                            oneOf: [
                                                {
                                                    properties: {
                                                        scheme: {
                                                            enum: ['bearer']
                                                        },
                                                        bearerFormat: {
                                                            type: 'string',
                                                            title: 'Format',
                                                        },
                                                        token: {
                                                            type: 'string',
                                                            title: 'Token',
                                                        },
                                                    }
                                                },
                                                {
                                                    properties: {
                                                        scheme: {
                                                            enum: ['basic']
                                                        },
                                                        username: {
                                                            type: 'string',
                                                            title: 'Username',
                                                        },
                                                        password: {
                                                            type: 'string',
                                                            title: 'Password',
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    }
                                }
                            ]
                        }
                    }
                }
            }
        }}
        uiSchema={{
            schema: {
                'ui:widget': function({ value = '', onChange }: FormWidgetProps) {
                    const { result: tools = [], execute } = useRequest<PluginTool[]>({
                        method: 'post',
                        url: `/plugin/parse`,
                        data: {
                            schema: value
                        }
                    }, {
                        manual: true,
                        setLoading(state) {
                            return {
                                ...state,
                                loading: true,
                            };
                        }
                    });

                    const debounceExecute = useDebounce(execute, 1000);

                    useEffect(() => {
                        debounceExecute();
                    }, [value]);

                    return <div>
                        <YamlEditor className={'mb-3'} value={value} onChange={onChange} />
                        <h6 className='mb-2 fs-7'>可用工具</h6>
                        {tools.length > 0 ? <div className='border rounded overflow-hidden'>
                                <table className='table table-borderless mb-0'>
                                    <thead>
                                    <tr>
                                        <td className='text-muted fw-bold'>名称</td>
                                        <td className='text-muted fw-bold'>描述</td>
                                        <td className='text-muted fw-bold'>费用</td>
                                        <td className='text-muted fw-bold text-center'>参数</td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    {tools.map(tool => {
                                        return <tr key={tool.name} className={'border-top'}>
                                            <td>{tool.title}</td>
                                            <td className='text-muted'>{tool.description}</td>
                                            <td className='text-muted'>{tool.fee} Token/次</td>
                                            <td width={60} className={'text-center'}>
                                                {tool.parameters ?
                                                    <ModalButton text={'查看'} modalProps={{
                                                        footer: false,
                                                        size: 'lg',
                                                        header: '参数信息'
                                                    }} variant={'link'}>
                                                        <table className='table table-borderless mb-0'>
                                                            <thead>
                                                            <tr>
                                                                <td className='text-muted fw-bold'>名称</td>
                                                                <td className='text-muted fw-bold'>描述</td>
                                                                <td className='text-muted fw-bold text-center'>类型</td>
                                                                <td className='text-muted fw-bold text-center'>必填</td>
                                                                <td className='text-muted fw-bold text-center'>默认</td>
                                                            </tr>
                                                            </thead>
                                                            <tbody>
                                                            {Object.entries(tool.parameters).map(([name, parameter]) => {
                                                                return <tr key={name} className={'border-top'}>
                                                                    <td>{name}</td>
                                                                    <td className='text-muted'>{parameter.description}</td>
                                                                    <td className='text-center'>{parameter.type}</td>
                                                                    <td className='text-center'>{parameter.required ? '是' : '否'}</td>
                                                                    <td className='text-center'>{parameter.default || '--'}</td>
                                                                </tr>;
                                                            })}
                                                            </tbody>
                                                        </table>
                                                    </ModalButton> : '无'}
                                            </td>
                                        </tr>;
                                    })}
                                    </tbody>
                                </table>
                            </div> :
                            <Alert variant='warning'>尚未解析到可用工具</Alert>
                        }
                    </div>;
                },
            },
            auth: {
                type: {
                    'ui:options': {
                        label: false
                    }
                }
            }
        }}
        onSuccess={onSuccess}
    />;
}
