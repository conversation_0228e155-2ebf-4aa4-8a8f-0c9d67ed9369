import { ModalForm } from '@topthink/common';

export default function ThinkConfig({ plugin, onSuccess }: { plugin: Plugin, onSuccess?: () => void }) {
    return <ModalForm
        text={'配置'}
        action={`/plugin/${plugin.id}/config`}
        modalProps={{
            header: 'ThinkAPI配置',
            scrollable: true,
            size: 'lg'
        }}
        transformData={(data) => ({ api: data })}
        formData={plugin.config?.api || []}
        omitExtraData
        schema={{
            type: 'array',
            items: {
                type: 'object',
                properties: {
                    uri: {
                        type: 'string',
                        title: '接口地址'
                    },
                    fee: {
                        type: 'number',
                        title: 'token消耗'
                    },
                    result_type: {
                        type: 'string',
                        title: '返回格式',
                        enum: ['json', 'image'],
                        enumNames: ['JSON', '图片'],
                        default: 'json'
                    }
                },
                dependencies: {
                    result_type: {
                        oneOf: [
                            {
                                properties: {
                                    result_type: {
                                        enum: ['image']
                                    },
                                    field: {
                                        type: 'string',
                                        title: '图片字段',
                                    }
                                }
                            }
                        ]
                    }
                },
                required: ['uri']
            }
        }}
        uiSchema={{
            items: {
                uri: {
                    'ui:col': 6,
                    'ui:placeholder': '请输入接口地址'
                },
                fee: {
                    'ui:col': 6,
                    'ui:placeholder': '请输入token消耗'
                }
            }
        }}
        onSuccess={onSuccess}
    />;
}
