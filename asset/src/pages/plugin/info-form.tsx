import { ModalForm, ModalFormProps, styled } from '@topthink/common';

export default function InfoForm(props: Omit<ModalFormProps, 'schema' | 'uiSchema'>) {
    return <ModalForm
        {...props}
        omitExtraData={true}
        schema={{
            type: 'object',
            properties: {
                icon: {
                    type: 'string',
                    title: '图标',
                },
                type: {
                    type: 'string',
                    title: '类型',
                },
                title: {
                    type: 'string',
                    title: '名称'
                },
                description: {
                    type: 'string',
                    title: '描述'
                },
                name: {
                    type: 'string',
                    title: '标识'
                },
                sort: {
                    type: 'number',
                    title: '排序',
                    default: 0
                }
            },
        }}
        uiSchema={{
            icon: {
                'ui:widget': 'avatar',
                'ui:options': {
                    as: Avatar,
                    endpoint: '/upload/plugin',
                    label: false,
                }
            },
            title: {
                'ui:autofocus': true,
                'ui:placeholder': '输入插件名称'
            },
            name: {
                'ui:placeholder': '输入标识（可选）',
                'ui:help': '留空则使用系统自动生成的标识'
            },
            description: {
                'ui:widget': 'textarea',
                'ui:placeholder': '输入插件描述',
                'ui:options': {
                    rows: 3
                }
            },
            type: {
                'ui:widget': 'typeahead',
                'ui:options': {
                    endpoint: '/plugin/types',
                    placeholder: '选择插件类型',
                    async: false
                }
            }
        }}
    />;
}

const Avatar = styled.div`
    margin: 0 auto;
`;
