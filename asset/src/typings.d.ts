declare module '*.svg' {
    import { ElementType } from 'react';
    const content: any;
    export default content;
    export const ReactComponent: ElementType;
}

interface Manifest {
    cloud: boolean;
}

interface Model {
    id: string;
}

interface Plugin {
    id: string;
    name: string;
    description: string;
    icon: string;
    auth: object;
    schema: string;
    api: string;
    config: any;
}

interface PluginTool {
    name: string;
    title: string;
    description: string;
    parameters: Record<string, {
        type: any
        title?: string
        description: string
        required?: boolean
        placeholder?: string
        encrypt?: boolean
        default?: string
        enum?: string[]
        enumNames?: string[]
        url?: string
        provider?: 'llm' | 'user'
    }> | null;
    fee: number;
}
