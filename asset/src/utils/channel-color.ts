// 根据渠道ID生成唯一的颜色
export const getChannelColor = (channelId?: number): string => {
    if (!channelId) return '#6c757d'; // 默认灰色

    // 使用渠道ID直接生成RGB颜色
    // 使用质数和位运算来生成分布均匀的颜色
    const r = (channelId * 95 + 131) % 256;
    const g = (channelId * 157 + 223) % 256;
    const b = (channelId * 213 + 149) % 256;

    // 转换为十六进制颜色代码
    return `#${r.toString(16).padStart(2, '0')}${g.toString(16).padStart(2, '0')}${b.toString(16).padStart(2, '0')}`;
};
