import { Loader, Result, useRequest } from '@topthink/common';
import get from 'lodash/get';
import { createContext, PropsWithChildren, useContext } from 'react';

const Context = createContext<Manifest | null>(null);

export default function ManifestProvider(props: PropsWithChildren) {
    const { result, error } = useRequest<Manifest>('/manifest');

    if (error) {
        return <Result status={'error'} title={error.message} />;
    }

    if (!result) {
        return <Loader />;
    }

    return <Context.Provider value={result}>
        {props.children}
    </Context.Provider>;
}

function useManifest(): Manifest;
function useManifest<TKey1 extends keyof Manifest>(key1: TKey1): Manifest[TKey1];
function useManifest<TKey1 extends keyof Manifest, TKey2 extends keyof Manifest[TKey1]>(key1: TKey1, key2: TKey2): Manifest[TKey1][TKey2];
function useManifest<TKey1 extends keyof Manifest, TKey2 extends keyof Manifest[TKey1], TKey3 extends keyof Manifest[TKey1][TKey2]>(key1: TKey1, key2: TKey2, key3: TKey3): Manifest[TKey1][TKey2][TKey3];
function useManifest(...path: any[]) {
    const manifest = useContext(Context);
    if (!manifest) {
        throw new Error('useManifest must be used within a ManifestProvider');
    }
    if (path.length > 0) {
        return get(manifest, path);
    }
    return manifest;
}

export { useManifest };
