import { Navigate, RouteObject, TabLayout } from '@topthink/common';
import Layout from './layout';

const routes: RouteObject[] = [
    {
        element: <Layout />,
        children: [
            {
                index: true,
                element: <Navigate to='model' replace />
            },
            {
                path: 'model',
                element: <TabLayout />,
                meta: {
                    icon: 'boxes',
                    title: '模型管理',
                    hideChildrenInMenu: true,
                },
                children: [
                    {
                        index: true,
                        element: <Navigate to={'chat'} replace />
                    },
                    {
                        path: 'chat',
                        lazy: () => import('@/pages/model/chat'),
                        meta: {
                            title: '会话'
                        }
                    },
                    {
                        path: 'image',
                        lazy: () => import('@/pages/model/image'),
                        meta: {
                            title: '图像'
                        }
                    },
                    {
                        path: 'video',
                        lazy: () => import('@/pages/model/video'),
                        meta: {
                            title: '视频'
                        }
                    },
                    {
                        path: 'audio',
                        lazy: () => import('@/pages/model/audio'),
                        meta: {
                            title: '语音'
                        }
                    },
                    {
                        path: 'text',
                        lazy: () => import('@/pages/model/text'),
                        meta: {
                            title: '文本'
                        }
                    },
                    {
                        path: 'music',
                        lazy: () => import('@/pages/model/music'),
                        meta: {
                            title: '音乐'
                        }
                    },
                ]
            },
            {
                path: 'plugin',
                meta: {
                    title: '插件管理',
                    icon: 'puzzle'
                },
                children: [
                    {
                        index: true,
                        lazy: () => import('@/pages/plugin'),
                    },
                ]
            },
            {
                path: 'channel',
                meta: {
                    title: '渠道管理',
                    icon: 'hdd-network'
                },
                children: [
                    {
                        index: true,
                        lazy: () => import('@/pages/channel'),
                    },
                ]
            },
            {
                path: 'key',
                meta: {
                    title: 'API Keys',
                    icon: 'key'
                },
                children: [
                    {
                        index: true,
                        lazy: () => import('@/pages/key'),
                    },
                ]
            },
            {
                path: 'admin',
                meta: {
                    title: '账号管理',
                    icon: 'people'
                },
                children: [
                    {
                        index: true,
                        lazy: () => import('@/pages/admin'),
                    },
                ]
            },
        ]
    }
];


export default routes;
