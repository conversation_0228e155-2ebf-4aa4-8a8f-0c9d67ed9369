services:
  app:
    image: registry.cn-shanghai.aliyuncs.com/topthink/ai:latest
    pull_policy: always
    restart: always
    command:
      - app:run
    ports:
      - "8080:80" #端口 可以根据需求修改
    environment:
      PHP_DB_HOST: mysql
      PHP_REDIS_HOST: redis
      PHP_LICENSE: "" #授权码 https://doc.topthink.com/@thinkai-deploy/apply.html
    volumes:
      - ./data/storage:/opt/htdocs/storage
    depends_on:
      - mysql
      - redis

  redis:
    image: registry.cn-shanghai.aliyuncs.com/topopen/redis:latest
    restart: always
    volumes:
      - ./data/redis:/data

  mysql:
    image: registry.cn-shanghai.aliyuncs.com/topopen/mysql:5.7
    restart: always
    environment:
      - MYSQL_ROOT_PASSWORD=root
      - MYSQL_DATABASE=ai
    volumes:
      - ./data/mysql:/var/lib/mysql
