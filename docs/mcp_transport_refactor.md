# MCP Transport 重构说明

## 重构概述

将 MCP Transport 从接口重构为抽象类，提取公共代码，减少重复实现。

## 重构内容

### 1. Transport 接口 → 抽象类

**之前 (Interface)**:
```php
interface Transport
{
    public function listTools();
    public function callTool(string $name, ?array $arguments = null);
    public function close();
    public static function connect($url): self;
}
```

**之后 (Abstract Class)**:
```php
abstract class Transport
{
    protected $id = 0;
    protected $sessionId = null;

    // 抽象方法
    abstract public function listTools();
    abstract public function callTool(string $name, ?array $arguments = null);
    abstract public function close();
    abstract public static function connect($url): self;

    // 公共方法
    protected static function getMessages(StreamInterface $stream);
    protected function getNextId(): int;
    protected function getSessionId(): ?string;
    protected function setSessionId(?string $sessionId): void;
}
```

### 2. 提取的公共方法

#### getMessages()
- **用途**: 解析 Server-Sent Events 流
- **之前**: ServerEvent 和 StreamableHttp 中各有一份相同的实现
- **之后**: 在 Transport 抽象类中统一实现

#### ID 管理方法
- `getNextId()`: 获取下一个请求 ID
- `getSessionId()`: 获取当前会话 ID  
- `setSessionId()`: 设置会话 ID

### 3. 子类修改

#### ServerEvent
```php
// 之前
class ServerEvent implements Transport
{
    protected $id = 0;
    // ... 重复的 getMessages 实现
}

// 之后
class ServerEvent extends Transport
{
    // 移除重复的属性和方法
    // 使用父类的公共方法
}
```

#### StreamableHttp
```php
// 之前
class StreamableHttp implements Transport
{
    protected $id = 0;
    protected $sessionId = null;
    // ... 重复的 getMessages 实现
}

// 之后
class StreamableHttp extends Transport
{
    // 移除重复的属性和方法
    // 使用父类的公共方法
}
```

## 重构优势

### 1. 代码复用
- 消除了 `getMessages()` 方法的重复实现
- 统一了 ID 和会话管理逻辑
- 减少了约 60 行重复代码

### 2. 维护性提升
- SSE 解析逻辑只需在一处维护
- 新增 Transport 实现时可直接使用公共方法
- 修改公共逻辑时只需修改一处

### 3. 一致性保证
- 所有 Transport 实现使用相同的 SSE 解析逻辑
- 统一的 ID 生成和会话管理机制
- 减少了不同实现间的差异

### 4. 扩展性增强
- 新的 Transport 实现可以继承公共功能
- 抽象类提供了清晰的实现模板
- 便于添加更多公共方法

## 兼容性

### 向后兼容
- 所有现有的 API 调用保持不变
- Toolkit 类无需修改
- 配置文件无需更改

### 类型兼容
- 子类仍然是 Transport 类型
- 多态性保持不变
- 接口契约保持一致

## 代码对比

### 重复代码消除

**之前**: 两个类中各有 30 行的 `getMessages` 实现
```php
// ServerEvent.php 中的 getMessages (30 行)
// StreamableHttp.php 中的 getMessages (30 行)
// 总计: 60 行重复代码
```

**之后**: 统一在抽象类中实现
```php
// Transport.php 中的 getMessages (30 行)
// 总计: 30 行，节省 50% 代码
```

### 属性管理优化

**之前**: 每个类管理自己的属性
```php
// ServerEvent
protected $id = 0;

// StreamableHttp  
protected $id = 0;
protected $sessionId = null;
```

**之后**: 统一在抽象类中管理
```php
// Transport
protected $id = 0;
protected $sessionId = null;
```

## 测试更新

添加了新的测试用例验证重构：
- 验证继承关系正确
- 验证抽象类特性
- 验证公共方法可用性
- 保持原有功能测试

## 总结

这次重构通过将 Transport 从接口改为抽象类，成功地：

1. **减少了代码重复**: 消除了约 50% 的重复代码
2. **提升了维护性**: 公共逻辑集中管理
3. **增强了一致性**: 统一的实现标准
4. **保持了兼容性**: 不影响现有代码
5. **改善了扩展性**: 便于添加新的 Transport 实现

这是一个典型的"提取超类"重构模式的成功应用，显著改善了代码质量和可维护性。
