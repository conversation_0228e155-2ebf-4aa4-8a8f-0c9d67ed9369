# MCP Streamable HTTP Transport 实现

## 概述

StreamableHttp 是 Model Context Protocol (MCP) 的一种传输方式，基于 HTTP 协议实现，支持 Server-Sent Events (SSE) 流式传输。这个实现遵循 MCP 规范 2025-06-18 版本。

## 特性

- 支持标准的 HTTP POST 请求发送 JSON-RPC 消息
- 支持 Server-Sent Events (SSE) 流式响应
- 自动处理会话管理 (Session Management)
- 支持协议版本协商
- 兼容 MCP 规范的安全要求

## 使用方法

### 1. 直接使用 StreamableHttp

```php
use app\lib\llm\mcp\StreamableHttp;

// 连接到 MCP 服务器
$client = StreamableHttp::connect('http://localhost:3000/mcp');

// 列出可用工具
$tools = $client->listTools();

// 调用工具
$result = $client->callTool('tool_name', ['param' => 'value']);

// 关闭连接
$client->close();
```

### 2. 通过 Toolkit 使用

```php
use app\lib\llm\mcp\Toolkit;

// 使用 streamable-http transport
$toolkit = new Toolkit('http://localhost:3000/mcp', 'streamable-http');

// 或者使用 http 别名
$toolkit = new Toolkit('http://localhost:3000/mcp', 'http');

// 获取工具
$tools = $toolkit->getTools();
```

## 实现细节

### 协议支持

- **协议版本**: 2025-06-18
- **传输方式**: HTTP POST + SSE
- **消息格式**: JSON-RPC 2.0
- **内容类型**: application/json, text/event-stream

### 请求头

每个请求都包含以下头部：

- `Accept`: application/json, text/event-stream
- `Content-Type`: application/json
- `MCP-Protocol-Version`: 2025-06-18
- `Mcp-Session-Id`: (如果服务器提供了会话 ID)

### 响应处理

1. **JSON 响应** (Content-Type: application/json)
   - 直接解析 JSON 并返回结果

2. **SSE 流响应** (Content-Type: text/event-stream)
   - 使用协程处理流式数据
   - 解析 SSE 事件并提取 JSON-RPC 消息

### 状态码处理

- `200 OK`: 成功响应
- `202 Accepted`: 通知和响应消息已接受
- `4xx/5xx`: 错误状态，抛出异常

## 与 ServerEvent 的区别

| 特性 | ServerEvent | StreamableHttp |
|------|-------------|----------------|
| 协议版本 | 2024-11-05 | 2025-06-18 |
| 连接方式 | GET 获取 SSE 流 | POST 发送请求 |
| 会话管理 | 无 | 支持 Session ID |
| 端点发现 | 通过 endpoint 事件 | 直接使用提供的 URL |

## 配置示例

在配置文件中使用 StreamableHttp transport：

```php
// config/llm.php
'tools' => [
    [
        'type' => 'mcp',
        'mcp' => [
            'name' => 'my-server',
            'url' => 'http://localhost:3000/mcp',
            'transport' => 'streamable-http', // 或 'http'
            'allowed' => ['tool1', 'tool2'],
        ],
    ],
],
```

## 安全注意事项

1. **Origin 验证**: 服务器应验证 Origin 头部防止 DNS 重绑定攻击
2. **本地绑定**: 本地服务器应只绑定到 localhost (127.0.0.1)
3. **身份验证**: 生产环境应实现适当的身份验证机制

## 错误处理

- 网络错误会抛出 `app\lib\llm\Exception`
- JSON-RPC 错误会解析并抛出包含错误信息的异常
- 超时错误会在指定时间后抛出异常

## 测试

运行测试：

```bash
php think test tests/mcp/StreamableHttpTest.php
```

## 参考

- [MCP 规范 - Transports](https://modelcontextprotocol.io/specification/2025-06-18/basic/transports)
- [Server-Sent Events 标准](https://html.spec.whatwg.org/multipage/server-sent-events.html)
- [JSON-RPC 2.0 规范](https://www.jsonrpc.org/specification)
