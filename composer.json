{"name": "yunwuxin/think", "description": "the new thinkphp framework", "type": "project", "keywords": ["framework", "thinkphp", "ORM"], "homepage": "https://www.thinkphp.cn/", "license": "Apache-2.0", "authors": [{"name": "liu21st", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "email": "<EMAIL>"}], "require": {"php": "^8.2.0", "topthink/framework": "^8.0", "topthink/think-swoole": "^4.0", "firebase/php-jwt": "^6.10", "guzzlehttp/guzzle": "^7.9", "symfony/polyfill-php84": "^1.31", "cebe/php-openapi": "^1.7", "topthink/think-tracing": "^1.0", "jcchavezs/zipkin-opentracing": "^2.0", "k8s/client": "^1.7", "k8s/http-guzzle": "^1.0", "k8s/ws-swoole": "^1.1", "nesbot/carbon": "^3.8", "yunwuxin/think-twig": "^3.0", "yunwuxin/think-auth": "^3.0", "topthink/think-orm": "4.0.x-dev", "topthink/think-migration": "^3.1", "topthink/think-annotation": "^3.0", "topthink/think-filesystem": "^3.0", "hashids/hashids": "^5.0", "topthinkcloud/client": "^1.1", "yunwuxin/think-cron": "^3.0"}, "require-dev": {"topthink/think-dumper": "^1.0", "mockery/mockery": "^1.6", "phpunit/phpunit": "^11.5", "topthink/think-ide-helper": "^2.0"}, "autoload": {"psr-4": {"app\\": "app"}}, "autoload-dev": {"psr-4": {"tests\\": "tests"}}, "config": {"preferred-install": "dist", "platform-check": false, "platform": {"ext-swoole": "4.6.0", "ext-fileinfo": "1.0.0"}, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "scripts": {"post-autoload-dump": ["@php think service:discover", "@php think vendor:publish"]}}