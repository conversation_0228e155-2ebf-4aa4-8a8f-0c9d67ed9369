<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateChannelTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('channel')
            ->addColumn(Column::string('name')->setComment('渠道名称'))
            ->addColumn(Column::string('driver')->setComment('驱动类'))
            ->addColumn(Column::json('auth')->setComment('认证信息，可以是数组实现负载均衡'))
            ->addColumn(Column::string('base_uri')->setNullable()->setComment('基础请求地址，可选'))
            ->addTimestamps()
            ->create();
    }
}
