<?php

use think\migration\Migrator;
use think\migration\db\Column;

class CreateModelTable extends Migrator
{
    /**
     * Change Method.
     *
     * Write your reversible migrations using this method.
     *
     * More information on writing migrations is available here:
     * http://docs.phinx.org/en/latest/migrations.html#the-abstractmigration-class
     *
     * The following commands can be used in this method and Phinx will
     * automatically reverse them when rolling back:
     *
     *    createTable
     *    renameTable
     *    addColumn
     *    renameColumn
     *    addIndex
     *    addForeignKey
     *
     * Remember to call "create()" or "update()" and NOT "save()" when working
     * with the Table class.
     */
    public function change()
    {
        $this->table('model')
            ->addColumn(Column::integer('channel_id')->setDefault(0)->setComment('渠道ID'))
            ->addColumn(Column::string('code'))
            ->addColumn(Column::string('label'))
            ->addColumn(Column::string('type'))
            ->addColumn(Column::string('description'))
            ->addColumn(Column::text('factor'))
            ->addColumn(Column::string('version')->setNullable())
            ->addColumn(Column::text('params')->setNullable())
            ->addColumn(Column::tinyInteger('sort')->setDefault(0))
            ->addColumn(Column::tinyInteger('status')->setDefault(1))
            ->create();
    }
}
