<?php

namespace app\command;

use app\model\Admin;
use think\console\Command;

class AdminReset extends Command
{
    protected function configure()
    {
        $this->setName('admin:reset');
    }

    public function handle()
    {
        if (!config('cloud.enable')) {
            //初始化管理员
            $admin = Admin::where('id', 1)->find();
            if (!$admin) {
                Admin::create([
                    'id'       => 1,
                    'username' => 'admin',
                    'password' => password_hash('admin', PASSWORD_DEFAULT),
                ]);
            } else {
                $admin->save([
                    'password' => password_hash('admin', PASSWORD_DEFAULT),
                ]);
            }
        }
    }
}
