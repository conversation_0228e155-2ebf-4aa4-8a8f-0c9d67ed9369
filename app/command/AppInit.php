<?php

namespace app\command;

use app\lib\License;
use app\model\Admin;
use think\console\Command;

class AppInit extends Command
{
    protected function configure()
    {
        $this->setName('app:init');
    }

    public function handle(License $license)
    {
        if (!config('cloud.enable')) {
            //初始化管理员
            $admin = Admin::where('id', 1)->find();
            if (!$admin) {
                Admin::create([
                    'id'       => 1,
                    'username' => 'admin',
                    'password' => password_hash('admin', PASSWORD_DEFAULT),
                ]);
            }
            //执行
            $license->verify();
        }
    }
}
