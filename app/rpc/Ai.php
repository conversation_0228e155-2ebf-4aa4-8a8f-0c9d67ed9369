<?php

namespace app\rpc;

use app\model\Model;
use app\model\Plugin;

class Ai
{
    public function getModels($type = null, $status = null)
    {
        return Model::list($type, $status)->select();
    }

    public function getPlugins()
    {
        return Plugin::list()->select();
    }

    public function getModelName($type, $code)
    {
        switch ($type) {
            case 'chat':
            case 'image':
            case 'text':
            case 'video':
            case 'audio':
                $model = \app\model\Model::where('code', $code)->where('type', $type)->find();
                return $model?->getAttr('label') ?? '未知模型';
            default:
                return '未知模型';
        }
    }
}
