<?php

namespace app\lib;

use think\exception\HttpException;
use Throwable;

class Hashids
{
    protected static function hashids($salt = '')
    {
        return new \Hashids\Hashids($salt, 8);
    }

    public static function encode($id, $salt = '')
    {
        return self::hashids($salt)->encode($id);
    }

    public static function decode($hash, $salt = '', $must = true)
    {
        try {
            [$id] = self::hashids($salt)->decode($hash);
            return $id;
        } catch (Throwable) {
            if ($must) {
                throw new HttpException(404);
            }
            return null;
        }
    }

}
