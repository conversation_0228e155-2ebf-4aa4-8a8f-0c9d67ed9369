<?php

namespace app\lib;

use app\model\Admin;
use Exception;
use Firebase\JWT\JWT;
use Firebase\JWT\Key;
use yunwuxin\auth\credentials\TokenCredentials;
use yunwuxin\auth\interfaces\Provider;

class UserProvider implements Provider
{
    public function retrieveByCredentials($credentials)
    {
        if ($credentials instanceof TokenCredentials) {
            $token = (string) $credentials->getToken();

            try {
                $decoded = (array) JWT::decode($token, new Key(config('app.token'), 'HS256'));
                $id      = $decoded['id'];

                return Admin::findOrFail($id);
            } catch (Exception) {

            }
        }
    }
}
