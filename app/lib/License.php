<?php

namespace app\lib;

use TopThinkCloud\Client;

/**
 * 授权验证类
 *
 * 提供授权验证功能，支持连续3次验证失败才标记为失败，验证成功即标记成功
 * 结果直接加密存储在runtime目录下
 */
class License
{
    // 存储文件名
    const LICENSE_FILE = 'license_data.bin';
    const PRODUCT = 'ai'; // 产品名称固定为ai
    const EXPIRATION_TIME = 3600; // 授权有效期为1小时（3600秒）

    /**
     * @var Client
     */
    protected $client;

    /**
     * @var string
     */
    protected $storagePath;

    /**
     * @var string
     */
    protected $license;

    /**
     * 构造函数
     *
     * @param Client $client 客户端实例
     */
    public function __construct(Client $client)
    {
        $this->client = $client;
        $this->storagePath = runtime_path() . self::LICENSE_FILE;
        $this->license = env('LICENSE', '');
    }

    /**
     * 验证授权
     *
     * @return void
     */
    public function verify(): void
    {
        // 如果没有授权码，直接返回（readData方法会返回默认的未授权状态）
        if (empty($this->license)) {
            return;
        }

        // 获取当前数据
        $data = $this->readData();

        try {
            // 尝试验证授权码，如果验证失败会抛出异常
            $this->client->license()->verify(self::PRODUCT, $this->license);

            // 如果执行到这里，说明验证成功，标记为成功并更新检查时间
            $data['status'] = true;
            $data['last_check'] = time();
            $this->saveData($data);
        } catch (\Throwable $e) {
            // 验证失败，但不改变状态，也不更新last_check
            // 这样在有效期内允许验证失败
        }
    }

    /**
     * 获取授权状态
     *
     * @return bool 授权是否有效
     */
    public function isValid(): bool
    {
        $data = $this->readData();
        $status = $data['status'] ?? false;

        // 检查授权是否过期（超过1小时没有重新验证）
        if ($status && isset($data['last_check'])) {
            $currentTime = time();
            $lastCheckTime = $data['last_check'];

            // 如果上次检查时间距离现在超过了设定的过期时间，则认为授权无效
            if (($currentTime - $lastCheckTime) > self::EXPIRATION_TIME) {
                return false;
            }
        }

        return $status;
    }

    /**
     * 读取存储的数据
     *
     * @return array 存储的数据
     */
    protected function readData(): array
    {
        if (!file_exists($this->storagePath) || empty($this->license)) {
            return [
                'status' => false,
                'last_check' => null
            ];
        }

        $encryptedData = file_get_contents($this->storagePath);
        $decryptedData = $this->decrypt($encryptedData);

        if ($decryptedData === false || !is_array($decryptedData)) {
            return [
                'status' => false,
                'last_check' => null
            ];
        }

        return $decryptedData;
    }

    /**
     * 保存数据到文件
     *
     * @param array $data 要保存的数据
     * @return bool 是否保存成功
     */
    protected function saveData(array $data): bool
    {
        if (empty($this->license)) {
            return false;
        }

        $encryptedData = $this->encrypt($data);
        return file_put_contents($this->storagePath, $encryptedData) !== false;
    }

    /**
     * 加密数据
     *
     * @param array $data 要加密的数据
     * @return string 加密后的字符串
     */
    protected function encrypt(array $data): string
    {
        $jsonData = json_encode($data);
        $ivlen = openssl_cipher_iv_length($cipher = 'AES-256-CBC');
        $iv = openssl_random_pseudo_bytes($ivlen);
        $encrypted = openssl_encrypt($jsonData, $cipher, $this->license, 0, $iv);
        return base64_encode($iv . $encrypted);
    }

    /**
     * 解密数据
     *
     * @param string $encryptedData 加密的数据
     * @return array|false 解密后的数据，失败返回false
     */
    protected function decrypt(string $encryptedData)
    {
        try {
            $data = base64_decode($encryptedData);
            $ivlen = openssl_cipher_iv_length($cipher = 'AES-256-CBC');
            $iv = substr($data, 0, $ivlen);
            $encrypted = substr($data, $ivlen);
            $decrypted = openssl_decrypt($encrypted, $cipher, $this->license, 0, $iv);

            if ($decrypted === false) {
                return false;
            }

            return json_decode($decrypted, true);
        } catch (\Throwable $e) {
            return false;
        }
    }
}
