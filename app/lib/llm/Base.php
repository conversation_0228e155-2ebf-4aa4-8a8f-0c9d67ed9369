<?php

namespace app\lib\llm;

use app\lib\Llm;

abstract class Base
{
    public function __construct(protected Llm $llm)
    {
    }

    protected function call($params, $method = null)
    {
        $type = strtolower(class_basename($this));

        if (is_null($method)) {
            $trace  = debug_backtrace(DEBUG_BACKTRACE_IGNORE_ARGS, 2);
            $method = $trace[1]['function'];
        }

        $driver = $this->llm->getDriver($type, $params['model'], $method);

        return call_user_func($driver, $params);
    }
}
