<?php

namespace app\lib\llm\channel;

use app\lib\Llm;
use app\lib\llm\Exception;
use Guzzle<PERSON>ttp\HandlerStack;
use GuzzleHttp\Middleware;
use GuzzleHttp\Utils;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\StreamInterface;
use rpc\contract\api\Api;
use think\helper\Arr;

abstract class Driver
{
    public const NAME = '';

    protected ?Llm $llm = null;

    public function __construct(protected array $options)
    {

    }

    public function setLlm(Llm $llm)
    {
        $this->llm = $llm;
        return $this;
    }

    public function getFeatures($model)
    {
        return [];
    }

    protected function getThinking($params)
    {
        $config   = $this->options['params']['thinking'] ?? ['is' => false];
        $is       = Arr::get($config, 'is', false);
        $closable = Arr::get($config, 'closable', false);

        if (!$is || !$closable) {
            return null;
        }

        $thinking = Arr::get($params, 'thinking', 'enabled');

        if ($thinking == 'auto' && !Arr::get($config, 'auto', false)) {
            return 'enabled';
        }

        return $thinking;
    }

    protected function applyFactor($usage, $extra = 0)
    {
        if (!empty($usage)) {
            $factor = $this->options['factor'] ?? 0;
            if (str_contains($factor, '/')) {
                [$promptFactor, $completionFactor, $cachedFactor] = explode('/', $factor) + [0, 0, 0];
            } else {
                $promptFactor = $completionFactor = $factor;
                $cachedFactor = 0;
            }

            $promptTokens = $usage['prompt_tokens'];
            $cachedTokens = $usage['prompt_tokens_details']['cached_tokens'] ?? 0;

            //额外增加5%的费率
            if ($cachedFactor > 0) {
                $promptTokens -= $cachedTokens;
            }

            $prompt     = (int) ceil(($promptTokens * (float) $promptFactor + $cachedTokens * (float) $cachedFactor) * 1.05);
            $completion = (int) ceil($usage['completion_tokens'] * (float) $completionFactor * 1.05);

            $usage = [
                'prompt_tokens'     => $prompt + $extra,
                'completion_tokens' => $completion,
                'total_tokens'      => $prompt + $completion + $extra,
            ];

            $this->llm?->consumeTokens($this->options['type'], $this->options['code'], $usage['total_tokens']);
        }
        return $usage;
    }

    protected function moderation($messages)
    {
        $content = array_reduce($messages, function ($carry, $item) {
            if (Arr::get($item, 'role') == 'user') {
                $content = Arr::get($item, 'content', '');
                if (is_array($content)) {
                    $content = array_reduce($content, function ($carry, $item) {
                        if (Arr::get($item, 'type') == 'text') {
                            return $carry . Arr::get($item, 'text', '') . "\n";
                        }
                        return $carry;
                    }, '');
                }
                return $carry . $content . "\n";
            }
            return $carry;
        }, '');

        if (!empty($content)) {
            $res = app(Api::class)->run('green/text_advance', [
                'service' => 'llm_query_moderation',
                'content' => $content,
            ]);

            $results = Arr::get($res, 'data.Result', []);
            $pass    = true;
            foreach ($results as $result) {
                if ($result['Label'] != 'nonLabel') {
                    $pass = false;
                    break;
                }
            }

            if (!$pass) {
                throw new Exception('内容可能包含违规或敏感信息');
            }
        }

        return 100;
    }

    abstract protected function getClientOptions();

    protected function transformRequest(RequestInterface $request)
    {
        return $request;
    }

    protected function getMessages(StreamInterface $response)
    {
        $buffer  = '';
        $message = null;
        while (!$response->eof()) {
            $text = $response->read(1);
            if ($text == "\r") {
                continue;
            }
            $buffer .= $text;
            if ($text == "\n") {
                if (empty($message)) {
                    $message = ['id' => '', 'event' => '', 'data' => ''];
                }
                if ($buffer == "\n") {
                    yield $message;
                    $message = null;
                } else {
                    if (preg_match('/data:(?<data>.*)/', trim($buffer), $match)) {
                        $message['data'] = trim($match['data']);
                    }
                }
                $buffer = '';
            }
        }
    }

    protected function getImageUrl($data)
    {
        if (empty($data)) {
            return null;
        }
        if (is_array($data)) {
            return array_map(function ($item) {
                return $this->getImageUrl($item);
            }, $data);
        }
        if (str_starts_with($data, 'data:image/')) {
            return $data;
        } elseif (str_starts_with($data, 'http')) {
            return "data:image/png;base64," . base64_encode(file_get_contents($data));
        } else {
            throw new Exception('image url must start with data:image/ or http');
        }
    }

    protected function getImageData($data, $base64 = true)
    {
        if (empty($data)) {
            return null;
        }
        if (str_starts_with($data, 'data:image/')) {
            $parts = explode(',', $data, 2);
            if (count($parts) == 2) {
                if (!$base64) {
                    return base64_decode($parts[1]);
                }
                return $parts[1];
            }
        } elseif (str_starts_with($data, 'http')) {
            $data = file_get_contents($data);
            if ($base64) {
                return base64_encode($data);
            }
            return $data;
        } else {
            throw new Exception('image url must start with data:image/ or http');
        }
    }

    /**
     * @return mixed
     */
    protected function transformResponse(ResponseInterface $response)
    {
        return $response;
    }

    protected function request($uri, $options = [], $method = 'POST')
    {
        $client = $this->getClient();

        $response = $client->request($method, $uri, $options);

        return $this->transformResponse($response);
    }

    protected function getAuth($key = null)
    {
        if (empty($key)) {
            return $this->options['auth'];
        }
        return Arr::get($this->options['auth'], $key, '');
    }

    protected function getBaseUri($default)
    {
        return $this->options['base_uri'] ?: $default;
    }

    protected function getClient()
    {
        $options = $this->getClientOptions();

        $handler = new HandlerStack(Utils::chooseHandler());

        $handler->push(Middleware::mapRequest(function (RequestInterface $request) {
            return $this->transformRequest($request);
        }));

        return new \GuzzleHttp\Client([
            'handler' => $handler,
            'verify'  => false,
            ...$options,
        ]);
    }
}
