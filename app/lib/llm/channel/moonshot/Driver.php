<?php

namespace app\lib\llm\channel\moonshot;

use app\lib\llm\channel\OpenAiCompatibleResponse;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = 'Moonshot';

    use OpenAiCompatibleResponse;

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://api.moonshot.cn'),
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth()}",
            ],
        ];
    }
}
