<?php

namespace app\lib\llm\channel\openai;

use app\lib\llm\contract\AudioInterface;
use app\lib\llm\traits\DefaultAudio;

class Audio extends Driver implements AudioInterface
{
    use DefaultAudio;

    public function speech($params)
    {
        $input = $params['input'] ?? '';
        $voice = $params['voice'] ?? '';
        $speed = $params['speed'] ?? 1;

        $response = $this->request('/v1/audio/speech', [
            'json' => [
                'model' => 'tts-1',
                'input' => $input,
                'voice' => $voice,
                'speed' => $speed,
            ],
        ]);

        $contentType = $response->getHeaderLine('Content-Type');

        $usage = mb_strlen($input);

        return [
            'audio' => [
                'type' => $contentType,
                'data' => base64_encode($response->getBody()->getContents()),
            ],
            'usage' => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function transcriptions($params)
    {
        $file = fopen($params['url'], 'r');
        try {
            $language = $params['language'] ?? null;

            $multipart = [
                [
                    'name'     => 'model',
                    'contents' => 'whisper-1',
                ],
                [
                    'name'     => 'file',
                    'contents' => $file,
                    'filename' => 'audio.mp3',
                ],
                [
                    'name'     => 'response_format',
                    'contents' => 'verbose_json',
                ],
            ];

            if ($language) {
                $multipart[] = [
                    'name'     => 'language',
                    'contents' => $language,
                ];
            }

            $result = $this->request('/v1/audio/transcriptions', [
                'multipart' => $multipart,
            ]);

            $duration = $result['duration'];

            return [
                'text'  => $result['text'],
                'usage' => $this->applyFactor([
                    'prompt_tokens'     => 0,
                    'completion_tokens' => $duration,
                    'total_tokens'      => $duration,
                ]),
            ];
        } finally {
            if (is_resource($file)) {
                fclose($file);
            }
        }
    }
}
