<?php

namespace app\lib\llm\channel\openai;

use app\lib\llm\contract\TextInterface;
use app\lib\llm\traits\DefaultText;
use think\helper\Arr;

class Text extends Driver implements TextInterface
{
    use DefaultText;

    public function embedding($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $input = $params['input'] ?? '';

        $multi = true;
        if (!is_array($input)) {
            $input = [$input];
            $multi = false;
        }

        $usage  = 0;
        $chunks = array_chunk($input, 10);

        $embeddings = array_reduce($chunks, function ($carry, $item) use ($model, &$usage, $multi) {
            $result = $this->request('/v1/embeddings', [
                'json' => [
                    'model' => $model,
                    'input' => $item,
                ],
            ]);

            $usage += Arr::get($result, 'usage.total_tokens', 0);

            return array_merge($carry, array_map(function ($item) {
                return Arr::get($item, 'embedding');
            }, Arr::get($result, 'data', [])));
        }, []);

        if (!$multi) {
            $embeddings = Arr::get($embeddings, '0', []);
        }

        return [
            'embeddings' => $embeddings,
            'usage'      => $this->applyFactor([
                'prompt_tokens'     => 0,
                'completion_tokens' => $usage,
                'total_tokens'      => $usage,
            ]),
        ];
    }
}
