<?php

namespace app\lib\llm\channel\openai;

use app\lib\llm\channel\OpenAiCompatibleResponse;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = 'OpenAI';

    use OpenAiCompatibleResponse;

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://api.openai.com'),
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth()}",
            ],
        ];
    }
}
