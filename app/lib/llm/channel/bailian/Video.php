<?php

namespace app\lib\llm\channel\bailian;

use app\lib\llm\contract\VideoInterface;
use think\helper\Arr;

class Video extends Driver implements VideoInterface
{

    public function getFeatures($model)
    {
        return match ($model) {
            'wanx2.1-i2v-turbo' => [
                'image'       => true,
                'resolutions' => ['480P', '720P'],
                'durations'   => [3, 4, 5],
            ],
            'wanx2.1-i2v-plus' => [
                'image'     => true,
                'durations' => [5],
            ],
            'wanx2.1-t2v-turbo' => [
                'text'      => true,
                'sizes'     => ['832x480', '480x832', '624x624', '1280x720', '720x1280', '960x960', '832x1088', '1088x832'],
                'durations' => [5],
            ],
            'wanx2.1-t2v-plus' => [
                'text'      => true,
                'sizes'     => ['1280x720', '720x1280', '960x960', '832x1088', '1088x832'],
                'durations' => [5],
            ],
            default => []
        };
    }

    protected function transformData(&$data, $model, $params)
    {
        switch ($model) {
            case 'wanx2.1-i2v-turbo':
            case 'wanx2.1-i2v-plus':
                $data['input']['img_url']         = $this->getImageUrl($params['image'] ?? '', $model);
                $data['parameters']['resolution'] = $params['resolution'] ?? '720P';
                break;
            case 'wanx2.1-t2v-turbo':
            case 'wanx2.1-t2v-plus':
                $data['parameters']['size'] = str_replace('x', '*', $params['size'] ?? '1280x720');
                break;
        }
    }

    public function generations($params)
    {
        $model  = $this->options['checkpoint'] ?? null;
        $prompt = $params['prompt'] ?? '';

        $input = [
            'prompt' => $prompt,
        ];

        $duration   = $params['duration'] ?? 5;
        $parameters = [
            'duration' => $duration,
        ];

        $data = [
            'model'      => $model,
            'input'      => $input,
            'parameters' => $parameters,
        ];

        $this->transformData($data, $model, $params);

        $result = $this->request('services/aigc/video-generation/video-synthesis', [
            'json'    => $data,
            'headers' => [
                'X-DashScope-Async' => 'enable',
            ],
        ]);

        $usage = $duration;

        return [
            'id'    => Arr::get($result, 'output.task_id'),
            'usage' => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function query($params)
    {
        $result = $this->request("tasks/{$params['id']}", [], 'GET');

        $status = match (Arr::get($result, 'output.task_status')) {
            'PENDING', 'RUNNING', 'SUSPENDED' => 'processing',
            'SUCCEEDED' => 'success',
            'FAILED', 'UNKNOWN' => 'fail',
        };

        switch ($status) {
            case 'success':
                $videos = [
                    [
                        'url' => Arr::get($result, 'output.video_url'),
                    ],
                ];
                break;
            case 'fail':
                $error = Arr::get($result, 'output.message');
                break;
        }

        return [
            'status' => $status,
            'videos' => $videos ?? null,
            'error'  => $error ?? null,
        ];
    }
}
