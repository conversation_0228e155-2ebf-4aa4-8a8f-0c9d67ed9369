<?php

namespace app\lib\llm\channel\bailian;

use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use think\helper\Arr;
use think\helper\Str;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = '百炼';

    protected function transformResponse(ResponseInterface $response)
    {
        $contentType = $response->getHeaderLine('Content-Type');

        if (Str::startsWith($contentType, 'text/event-stream')) {
            return $response->getBody();
        }

        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;

        $result = json_decode($response->getBody()->getContents(), true);

        if (!$isOk) {
            throw new Exception(Arr::get($result, 'message', 'Unknown error'));
        }

        return $result;
    }

    protected function getImageUrl($image, $model = null)
    {
        // 步骤1：获取文件上传凭证
        $result = $this->request('uploads', [
            'query' => [
                'action' => 'getPolicy',
                'model'  => $model,
            ],
        ], 'GET');

        $policy = $result['data'];

        // 获取图片数据（二进制格式）
        $imageData = $this->getImageData($image, false);

        // 生成文件名（使用时间戳和随机字符串避免重复）
        $fileName = time() . '_' . Str::random(8) . '.png';

        // 构建完整的 key 路径
        $key = $policy['upload_dir'] . '/' . $fileName;

        // 步骤2：上传文件到 OSS
        // 手动构建 multipart/form-data 请求体
        $boundary = '----formdata-' . uniqid();
        $body     = '';

        // 添加表单字段
        $fields = [
            'key'                    => $key,
            'policy'                 => $policy['policy'],
            'OSSAccessKeyId'         => $policy['oss_access_key_id'],
            'success_action_status'  => '200',
            'Signature'              => $policy['signature'],
            'x-oss-object-acl'       => $policy['x_oss_object_acl'],
            'x-oss-forbid-overwrite' => $policy['x_oss_forbid_overwrite'],
        ];

        foreach ($fields as $name => $value) {
            $body .= "--{$boundary}\r\n";
            $body .= "Content-Disposition: form-data; name=\"{$name}\"\r\n\r\n";
            $body .= "{$value}\r\n";
        }

        // 添加文件字段（必须是最后一个）
        $body .= "--{$boundary}\r\n";
        $body .= "Content-Disposition: form-data; name=\"file\"; filename=\"{$fileName}\"\r\n";
        $body .= "Content-Type: image/png\r\n\r\n";
        $body .= $imageData . "\r\n";
        $body .= "--{$boundary}--\r\n";

        $client = new \GuzzleHttp\Client(['verify' => false]);

        $client->request('POST', $policy['upload_host'], [
            'headers' => [
                'Content-Type'   => "multipart/form-data; boundary={$boundary}",
                'Content-Length' => strlen($body),
            ],
            'body'    => $body,
        ]);

        // 步骤3：返回 OSS URL
        return 'oss://' . $key;
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://dashscope.aliyuncs.com/api/v1/'),
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth()}",
            ],
        ];
    }
}
