<?php

namespace app\lib\llm\channel\bailian;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\Exception;
use app\lib\llm\traits\DefaultImage;
use think\helper\Arr;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function getFeatures($model)
    {
        return match ($model) {
            'wanx-v1' => [
                'ref'    => [
                    'repaint' => '参考内容',
                    'refonly' => '参考风格',
                ],
                'n'      => 4,
                'sizes'  => ['1024x1024', '720x1080', '1080x720'],
                'seed'   => true,
                'styles' => [
                    [
                        'name' => '默认',
                        'code' => null,
                    ],
                    [
                        'name' => '3D卡通',
                        'code' => '<3d cartoon>',
                    ],
                    [
                        'name' => '动画',
                        'code' => '<anime>',
                    ],
                    [
                        'name' => '油画',
                        'code' => '<oil painting>',
                    ],
                    [
                        'name' => '水彩',
                        'code' => '<watercolor>',
                    ],
                    [
                        'name' => '素描',
                        'code' => '<sketch>',
                    ],
                    [
                        'name' => '中国画',
                        'code' => '<chinese painting>',
                    ],
                    [
                        'name' => '扁平插画',
                        'code' => '<flat illustration>',
                    ],
                ],
            ],
            'wanx2.1-t2i-plus', 'wanx2.1-t2i-turbo', 'wanx2.0-t2i-turbo' => [
                'seed'  => true,
                'sizes' => ['1024x1024', '720x1080', '1080x720'],
                'n'     => 4,
            ],
            default => []
        };
    }

    public function generations($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $prompt = $params['prompt'] ?? '';
        $size   = $params['size'] ?? '1024x1024';
        $n      = $params['n'] ?? 1;
        $format = $params['response_format'] ?? 'url';

        if ($format != 'url') {
            throw new Exception("not support format: {$format}");
        }

        $data = [
            'model'      => $model,
            'input'      => [
                'prompt' => $prompt,
            ],
            'parameters' => [
                'size' => str_replace('x', '*', $size),
                'n'    => (int) $n,
            ],
        ];

        $style = $params['style'] ?? null;
        if (!empty($style)) {
            $data['parameters']['style'] = $style;
        }

        $refImg = $params['image']['url'] ?? null;
        if (!empty($refImg)) {
            $data['input']['ref_img'] = $refImg;

            $refStrength = $params['image']['strength'] ?? null;
            $refMode     = $params['image']['mode'] ?? null;
            if (!empty($refStrength)) {
                $data['parameters']['ref_strength'] = $refStrength;
            }
            if (!empty($refMode)) {
                $data['parameters']['ref_mode'] = $refMode;
            }
        }

        if (!empty($params['seed'])) {
            $data['parameters']['seed'] = (int) $params['seed'];
        }

        $result = $this->request('services/aigc/text2image/image-synthesis', [
            'json'    => $data,
            'headers' => [
                'X-DashScope-Async' => 'enable',
            ],
        ]);

        $taskId = Arr::get($result, 'output.task_id');
        $result = $this->waitTask($taskId);
        $images = array_values(array_filter($result['output']['results'], function ($value) {
            return isset($value['url']);
        }));
        $usage  = $result['usage']['image_count'];

        return [
            'images' => $images,
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function edit($params)
    {
        $prompt = $params['prompt'] ?? '';
        $image  = $params['image'] ?? null;
        $format = $params['response_format'] ?? 'url';

        if ($format != 'url') {
            throw new Exception("not support format: {$format}");
        }

        $data = [
            'model'      => 'wanx2.1-imageedit',
            'input'      => [
                'prompt'         => $prompt,
                'function'       => 'description_edit',
                'base_image_url' => $image,
                // 'base_image_url' => $this->getImageUrl($image, 'wanx2.1-imageedit'), //TODO 尚未支持
            ],
            'parameters' => [
                'n' => 1,
            ],
        ];

        $result = $this->request('services/aigc/image2image/image-synthesis', [
            'json'    => $data,
            'headers' => [
                'X-DashScope-Async' => 'enable',
            ],
        ]);

        $taskId = Arr::get($result, 'output.task_id');

        $result = $this->waitTask($taskId);

        $images = $result['output']['results'];

        return [
            'images' => $images,
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => 1,
                'completion_tokens' => 0,
                'total_tokens'      => 1,
            ]),
        ];
    }

    public function poster($params)
    {
        $title    = $params['title'];
        $subTitle = $params['sub_title'] ?? '';
        $body     = $params['body'] ?? '';
        $prompt   = $params['prompt'] ?? '';
        $style    = $params['style'] ?? '';
        $size     = $params['size'] ?? 'vertical';
        $n        = $params['n'] ?? 1;

        $data = [
            'model'      => 'wanx-poster-generation-v1',
            'input'      => array_filter_null([
                'title'          => $title,
                'sub_title'      => $subTitle,
                'body_text'      => $body,
                'prompt_text_zh' => $prompt,
                'wh_ratios'      => $size == 'vertical' ? '竖版' : '横版',
                'lora_name'      => $style,
                'generate_mode'  => 'generate',
                'generate_num'   => $n,
            ]),
            'parameters' => new \ArrayObject(),
        ];

        $result = $this->request('services/aigc/text2image/image-synthesis', [
            'json'    => $data,
            'headers' => [
                'X-DashScope-Async' => 'enable',
            ],
        ]);

        $taskId = Arr::get($result, 'output.task_id');

        $result = $this->waitTask($taskId);

        $images = array_map(function ($url) {
            return ['url' => $url];
        }, $result['output']['render_urls']);

        $usage = $result['usage']['image_count'];

        return [
            'images' => $images,
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    protected function waitTask($id)
    {
        sleep(10);

        $waited = 0;
        //获取任务结果
        while (true) {
            $waited += 3;
            sleep(3);
            $result = $this->request("tasks/{$id}", method: 'GET');
            $status = Arr::get($result, 'output.task_status');

            if ($status == 'FAILED') {
                throw new Exception("task failed: {$result['output']['message']}");
            } elseif ($status == 'SUCCEEDED') {
                return $result;
            }

            if ($waited > 240) {
                throw new Exception('task timeout');
            }
        }
    }
}
