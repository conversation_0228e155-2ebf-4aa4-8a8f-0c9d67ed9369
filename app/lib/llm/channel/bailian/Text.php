<?php

namespace app\lib\llm\channel\bailian;

use app\lib\llm\contract\TextInterface;
use app\lib\llm\traits\DefaultText;
use think\helper\Arr;

class Text extends Driver implements TextInterface
{
    use DefaultText;

    public function embedding($params)
    {
        $input = $params['input'] ?? '';

        $multi = true;
        if (!is_array($input)) {
            $input = [$input];
            $multi = false;
        }

        $usage  = 0;
        $chunks = array_chunk($input, 6);

        $embeddings = array_reduce($chunks, function ($carry, $item) use (&$usage, $multi) {
            $result = $this->request('services/embeddings/text-embedding/text-embedding', [
                'json' => [
                    'model'      => 'text-embedding-v3',
                    'input'      => [
                        'texts' => $item,
                    ],
                    'parameters' => [
                        'text_type' => $multi ? 'document' : 'query',
                        'dimension' => 1024,
                    ],
                ],
            ]);

            $usage += Arr::get($result, 'usage.total_tokens', 0);

            return array_merge($carry, array_map(function ($item) {
                return Arr::get($item, 'embedding');
            }, Arr::get($result, 'output.embeddings', [])));
        }, []);

        if (!$multi) {
            $embeddings = Arr::get($embeddings, '0', []);
        }

        return [
            'embeddings' => $embeddings,
            'usage'      => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function rerank($params)
    {
        $result = $this->request('services/rerank/text-rerank/text-rerank', [
            'json' => [
                'model'      => 'gte-rerank',
                'input'      => [
                    'query'     => $params['query'],
                    'documents' => $params['documents'],
                ],
                'parameters' => [
                    'return_documents' => true,
                ],
            ],
        ]);

        $usage = Arr::get($result, 'usage.total_tokens', 0);

        return [
            'documents' => array_map(function ($item) {
                return $item['document']['text'];
            }, array_filter(Arr::get($result, 'output.results', []), function ($item) use ($params) {
                return $item['relevance_score'] >= ($params['score'] ?? 0);
            })),
            'usage'     => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }
}
