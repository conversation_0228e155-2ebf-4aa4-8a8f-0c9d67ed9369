<?php

namespace app\lib\llm\channel\bailian;

use app\lib\llm\contract\ChatInterface;
use app\lib\llm\Exception;
use Psr\Http\Message\StreamInterface;
use think\helper\Arr;

class Chat extends Driver implements ChatInterface
{

    public function completions($params)
    {
        $multi = $this->options['params']['vision'] ?? false;

        $model    = $this->options['checkpoint'] ?? '';
        $messages = $params['messages'] ?? [];
        $tools    = $params['tools'] ?? null;
        $stream   = $params['stream'] ?? true;

        //content必传
        $messages = array_map(function ($message) {
            if (!isset($message['content'])) {
                $message['content'] = '';
            }
            return $message;
        }, $messages);

        if (!empty($tools)) {
            $tools = array_map(function ($tool) {
                if (isset($tool['function'])) {
                    $tool['function']['parameters'] = json_encode($tool['function']['parameters'] ?? []);
                }
                return $tool;
            }, $tools);
        }

        $url = $multi ? 'services/aigc/multimodal-generation/generation' : 'services/aigc/text-generation/generation';

        $thinking = $this->getThinking($params);

        $res = $this->request($url, [
            'json'    => [
                'model'      => $model,
                'input'      => [
                    'messages' => $messages,
                ],
                'parameters' => [
                    'tools'              => $tools,
                    'temperature'        => $params['temperature'] ?? null,
                    'max_tokens'         => $params['max_tokens'] ?? null,
                    'seed'               => $params['seed'] ?? null,
                    'enable_thinking'    => $thinking ? ($thinking == 'enabled') : null,
                    'incremental_output' => $stream ? true : null,
                    'result_format'      => 'message',
                    'response_format'    => $params['response_format'] ?? null,
                ],
            ],
            'stream'  => $stream,
            'headers' => [
                'X-DashScope-SSE' => $stream ? 'enable' : null,
            ],
        ]);

        if ($res instanceof StreamInterface) {
            $call         = null;
            $usage        = null;
            $finishReason = null;
            foreach ($this->getMessages($res) as $message) {
                $data  = $message['data'];
                $event = json_decode($data, true);
                if (!empty($event['output'])) {
                    if (!empty($event['usage'])) {
                        $usage = $this->adjustPromptCachedTokens($event['usage']);
                    }
                    $output = $event['output'];
                    if (!empty($output['choices'][0]['finish_reason'])) {
                        $finishReason = $output['choices'][0]['finish_reason'];
                    }
                    $message = $output['choices'][0]['message'];
                    $delta   = null;

                    //检查是否为tools_call
                    if (!empty($message['tool_calls'])) {
                        $current = $message['tool_calls'][0];

                        if ($call) {
                            $call['function']['arguments'] .= $current['function']['arguments'] ?? '';
                        } else {
                            $call['index'] = 0;

                            $call = $current;
                        }
                    } else {
                        if (strlen($message['reasoning_content'] ?? '') > 0) {
                            $delta = [
                                'role'      => 'assistant',
                                'reasoning' => $message['reasoning_content'],
                            ];
                        } elseif (isset($message['content'])) {
                            if (is_array($message['content'])) {
                                $content = array_reduce($message['content'], function ($carry, $item) {
                                    return $carry . $item['text'];
                                }, '');

                                $delta = [
                                    'role'    => 'assistant',
                                    'content' => $content,
                                ];
                            } else {
                                $delta = [
                                    'role'    => 'assistant',
                                    'content' => $message['content'],
                                ];
                            }
                        }

                        if (!empty($delta)) {
                            yield [
                                'delta'         => $delta,
                                'finish_reason' => null,
                            ];
                        }
                    }
                } else {
                    throw new Exception(Arr::get($event, 'message', 'Unknown error'));
                }
            }

            if ($call) {
                yield [
                    'delta'         => [
                        'role'       => 'assistant',
                        'content'    => null,
                        'tool_calls' => [$call],
                    ],
                    'finish_reason' => null,
                ];
                $call = null;
            }

            yield [
                'usage'         => $this->applyFactor($usage),
                'finish_reason' => $finishReason ?? 'stop',
            ];
        } else {
            //百炼的思考模型不支持非stream输出 无需处理
            $message = $res['output']['choices'][0]['message'];

            $finishReason = $res['output']['choices'][0]['finish_reason'];
            $usage        = $this->adjustPromptCachedTokens($res['usage']);

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }

    protected function adjustPromptCachedTokens($usage)
    {
        $newUsage = [
            'prompt_tokens'     => $usage['input_tokens'],
            'completion_tokens' => $usage['output_tokens'],
            'total_tokens'      => $usage['total_tokens'],
        ];

        if (!empty($usage['prompt_tokens_details'])) {
            $newUsage['prompt_tokens_details'] = $usage['prompt_tokens_details'];
        }

        return $newUsage;
    }
}
