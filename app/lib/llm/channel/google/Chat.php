<?php

namespace app\lib\llm\channel\google;

use app\lib\llm\contract\ChatInterface;
use Psr\Http\Message\StreamInterface;

class Chat extends Driver implements ChatInterface
{
    const REASONING_START = "<thought>";
    const REASONING_END   = "</thought>";

    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $messages    = $params['messages'] ?? [];
        $tools       = $params['tools'] ?? [];
        $stream      = $params['stream'] ?? true;
        $temperature = $params['temperature'] ?? null;

        $json = [
            'model'           => $model,
            'messages'        => $messages,
            'tools'           => $tools,
            'temperature'     => $temperature,
            'stream'          => $stream,
            'stream_options'  => [
                'include_usage' => true,
            ],
            'response_format' => $params['response_format'] ?? null,
        ];

        $thinking = $this->getThinking($params);
        if ($thinking) {
            $json['extra_body'] = [
                'google' => [
                    'thinking_config' => [
                        'include_thoughts' => $thinking == 'enabled',
                    ],
                ],
            ];
        }

        $res = $this->request("chat/completions", [
            'json'   => $json,
            'stream' => $stream,
        ]);

        $startLength = mb_strlen(self::REASONING_START);
        $endLength   = mb_strlen(self::REASONING_END);

        if ($res instanceof StreamInterface) {
            $call         = null;
            $finishReason = null;
            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];
                if ($data == '[DONE]') {
                    break;
                }

                $result = json_decode($data, true);

                $delta        = $result['choices'][0]['delta'] ?? null;
                $usage        = $result['usage'] ?? null;
                $finishReason = $result['choices'][0]['finish_reason'] ?? $finishReason;

                //检查是否为tools_call
                if (!empty($delta['tool_calls'])) {
                    $current = $delta['tool_calls'][0];
                    if ($call) {
                        if ($call['index'] != $current['index']) {
                            yield [
                                'delta'         => [
                                    'role'       => 'assistant',
                                    'content'    => null,
                                    'tool_calls' => [$call],
                                ],
                                'finish_reason' => null,
                            ];
                            $call = $current;
                        } else {
                            $call['function']['arguments'] .= $current['function']['arguments'];
                        }
                    } else {
                        $call = $current;
                    }
                } else {
                    if (strlen($delta['content'] ?? '') > 0) {
                        if ($delta['extra_content']['google']['thought'] ?? false) {
                            $delta['reasoning'] = str_replace(self::REASONING_START, '', $delta['content']);
                            unset($delta['content']);
                        } else {
                            $delta['content'] = str_replace(self::REASONING_END, '', $delta['content']);
                        }
                        yield [
                            'delta'         => $delta,
                            'finish_reason' => null,
                        ];
                    }
                }

                if (!empty($finishReason) && !empty($usage)) {
                    if ($call) {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [$call],
                            ],
                            'finish_reason' => null,
                        ];
                        $call = null;
                    }
                    yield [
                        'usage'         => $this->applyFactor($usage),
                        'finish_reason' => $finishReason,
                    ];
                }
            }
        } else {
            $message      = $res['choices'][0]['message'];
            $finishReason = $res['choices'][0]['finish_reason'];
            $usage        = $res['usage'];

            if (str_starts_with($message['content'] ?? '', self::REASONING_START)) {
                $message['reasoning'] = substr($message['content'], $startLength, strpos($message['content'], self::REASONING_END) - $startLength);
                $message['content']   = substr($message['content'], strpos($message['content'], self::REASONING_END) + $endLength);
            }

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }
}
