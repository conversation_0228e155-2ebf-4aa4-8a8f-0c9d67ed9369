<?php

namespace app\lib\llm\channel\google;

use app\lib\llm\channel\OpenAiCompatibleResponse;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = 'Google';

    use OpenAiCompatibleResponse;

    protected function getError($result)
    {
        return $result[0]['error']['message'] ?? 'Unknow error';
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://generativelanguage.googleapis.com/v1beta/openai/'),
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth()}",
            ],
        ];
    }
}
