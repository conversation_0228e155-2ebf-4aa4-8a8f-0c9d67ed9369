<?php

namespace app\lib\llm\channel\hunyuan;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\Exception;
use app\lib\llm\traits\DefaultImage;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function getFeatures($model)
    {
        return [
            'sizes'  => [
                '768x768',
                '768x1024',
                '1024x768',
                '1024x1024',
                '720x1280',
                '1280x720',
                '768x1280',
                '1280x768',
            ],
            'styles' => [
                [
                    'name' => '智能推荐',
                    'code' => null,
                ],
                [
                    'name' => '日漫动画',
                    'code' => 'riman',
                ],
                [
                    'name' => '水墨画',
                    'code' => 'shuimo',
                ],
                [
                    'name' => '莫奈',
                    'code' => 'monai',
                ],
                [
                    'name' => '扁平插画',
                    'code' => 'bianping',
                ],
                [
                    'name' => '像素插画',
                    'code' => 'xiangsu',
                ],
                [
                    'name' => '儿童绘本',
                    'code' => 'ertonghuiben',
                ],
                [
                    'name' => '3D 渲染',
                    'code' => '3dxuanran',
                ],
                [
                    'name' => '漫画',
                    'code' => 'manhua',
                ],
                [
                    'name' => '黑白漫画',
                    'code' => 'heibaimanhua',
                ],
                [
                    'name' => '写实',
                    'code' => 'xieshi',
                ],
                [
                    'name' => '动漫',
                    'code' => 'dongman',
                ],
                [
                    'name' => '毕加索',
                    'code' => 'bijiasuo',
                ],
                [
                    'name' => '赛博朋克',
                    'code' => 'saibopengke',
                ],
                [
                    'name' => '油画',
                    'code' => 'youhua',
                ],
                [
                    'name' => '马赛克',
                    'code' => 'masaike',
                ],
                [
                    'name' => '青花瓷',
                    'code' => 'qinghuaci',
                ],
                [
                    'name' => '新年剪纸画',
                    'code' => 'xinnianjianzhi',
                ],
                [
                    'name' => '新年花艺',
                    'code' => 'xinnianhuayi',
                ],
            ],
        ];
    }

    public function generations($params)
    {
        $prompt = $params['prompt'] ?? '';
        $size   = $params['size'] ?? '1024x1024';

        $result = $this->request('/', [
            'headers' => [
                'X-TC-Action'  => 'SubmitHunyuanImageJob',
                'X-TC-Version' => '2023-09-01',
                'X-TC-Region'  => 'ap-guangzhou',
            ],
            'json'    => array_filter_null([
                'Prompt'     => $prompt,
                'Resolution' => str_replace('x', ':', $size),
                'Style'      => $params['style'] ?? null,
                'LogoAdd'    => 0,
            ]),
        ]);

        $jobId = $result['JobId'];

        sleep(5);

        $waited = 0;
        //获取任务结果
        while (true) {
            $waited += 3;
            sleep(3);
            $result = $this->request("/", [
                'headers' => [
                    'X-TC-Action'  => 'QueryHunyuanImageJob',
                    'X-TC-Version' => '2023-09-01',
                    'X-TC-Region'  => 'ap-guangzhou',
                ],
                'json'    => [
                    'JobId' => $jobId,
                ],
            ]);

            $status = $result['JobStatusCode'];

            if ($status == 4) {
                throw new Exception("task failed: {$result['JobErrorMsg']}");
            } elseif ($status == 5) {
                $images = array_map(function ($url) {
                    return [
                        'url' => $url,
                    ];
                }, $result['ResultImage']);
                break;
            }

            if ($waited > 240) {
                throw new Exception('task timeout');
            }
        }

        $usage = 1;

        return [
            'images' => $images,
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }
}
