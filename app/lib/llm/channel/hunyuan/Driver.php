<?php

namespace app\lib\llm\channel\hunyuan;

use app\lib\llm\Exception;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use think\helper\Str;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = '混元';

    protected $service = 'hunyuan';

    protected function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;

        if (!$isOk) {
            throw new Exception('Unknown error');
        }

        $contentType = $response->getHeaderLine('Content-Type');

        if (Str::startsWith($contentType, 'text/event-stream')) {
            return $response->getBody();
        }

        $content = $response->getBody()->getContents();
        $result  = json_decode($content, true);

        $res = $result['Response'];

        if (isset($res['Error'])) {
            throw new Exception($res['Error']['Message']);
        }

        return $res;
    }

    protected function transformRequest(RequestInterface $request)
    {
        $method = $request->getMethod();
        $url    = (string) $request->getUri();
        $body   = $request->getBody()->getContents();

        $timestamp = time();
        $date      = gmdate('Y-m-d', $timestamp);
        $algorithm = 'TC3-HMAC-SHA256';

        $bodyHex = hash('sha256', $body);
        $path    = parse_url($url, PHP_URL_PATH);
        $query   = parse_url($url, PHP_URL_QUERY);

        $canonicalHeaders = join("\n", [
            'content-type:' . $request->getHeaderLine('content-type'),
            'host:' . $request->getHeaderLine('host'),
        ]);

        $signedHeaders = implode(';', [
            'content-type',
            'host',
        ]);

        $canonicalRequest = join("\n", [
            $method,
            $path,
            $query,
            $canonicalHeaders,
            '',
            $signedHeaders,
            $bodyHex,
        ]);

        $credentialScope        = $date . '/' . $this->service . '/tc3_request';
        $hashedCanonicalRequest = hash('SHA256', $canonicalRequest);

        $stringToSign = join("\n", [
            $algorithm,
            $timestamp,
            $credentialScope,
            $hashedCanonicalRequest,
        ]);

        $secretDate    = hash_hmac('SHA256', $date, 'TC3' . $this->getAuth('secret_key'), true);
        $secretService = hash_hmac('SHA256', $this->service, $secretDate, true);
        $secretSigning = hash_hmac('SHA256', 'tc3_request', $secretService, true);
        $signature     = hash_hmac('SHA256', $stringToSign, $secretSigning);

        $authorization = $algorithm
            . ' Credential=' . $this->getAuth('access_key') . '/' . $credentialScope
            . ', SignedHeaders=' . $signedHeaders . ', Signature=' . $signature;

        return $request
            ->withHeader('X-TC-Timestamp', $timestamp)
            ->withHeader('Authorization', $authorization);
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://hunyuan.tencentcloudapi.com'),
        ];
    }
}
