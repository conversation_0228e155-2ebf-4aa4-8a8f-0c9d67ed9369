<?php

namespace app\lib\llm\channel\hunyuan;

use app\lib\llm\contract\ChatInterface;
use app\lib\llm\Exception;
use Psr\Http\Message\StreamInterface;

class Chat extends Driver implements ChatInterface
{
    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $messages    = $params['messages'] ?? [];
        $tools       = $params['tools'] ?? [];
        $stream      = $params['stream'] ?? true;
        $temperature = $params['temperature'] ?? 0.5;

        $messages = array_map(function ($message) {
            $msg = [
                'Role' => $message['role'],
            ];

            $content = $message['content'] ?? '';
            if (is_array($content)) {
                $msg['Contents'] = array_map(function ($item) {
                    return match ($item['type']) {
                        'text' => [
                            'Type' => 'text',
                            'Text' => $item['text'],
                        ],
                        'image_url' => [
                            'Type'     => 'image_url',
                            'ImageUrl' => [
                                'Url' => $item['image_url']['url'] ?? null,
                            ],
                        ],
                        default => throw new Exception('not support content type: ' . $item['type'])
                    };
                }, $content);
            } else {
                $msg['Content'] = $content;
            }

            if (!empty($message['tool_call_id'])) {
                $msg['ToolCallId'] = $message['tool_call_id'];
            }

            if (!empty($message['tool_calls'])) {
                $msg['ToolCalls'] = array_map(function ($call) {
                    return [
                        'Id'       => $call['id'],
                        'Type'     => $call['type'],
                        'Function' => [
                            'Name'      => $call['function']['name'],
                            'Arguments' => $call['function']['arguments'],
                        ],
                    ];
                }, $message['tool_calls']);
            }

            return $msg;
        }, $messages);

        if (!empty($tools)) {
            $tools = array_map(function ($item) {
                return [
                    'Type'     => $item['type'],
                    'Function' => [
                        'Name'        => $item['function']['name'],
                        'Parameters'  => !empty($item['function']['parameters']) ? json_encode($item['function']['parameters']) : null,
                        'Description' => $item['function']['description'] ?? null,
                    ],
                ];
            }, $tools);
        }

        $json = [
            'Model'       => $model,
            'Messages'    => $messages,
            'Stream'      => $stream,
            'Temperature' => $temperature * 2,
            'Tools'       => $tools,
        ];

        $thinking = $this->getThinking($params);
        if ($thinking) {
            $json['EnableThinking'] = $thinking == 'enabled';
        }

        $res = $this->request('/', [
            'headers' => [
                'X-TC-Action'  => 'ChatCompletions',
                'X-TC-Version' => '2023-09-01',
            ],
            'json'    => $json,
            'stream'  => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $call = null;
            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];
                if ($data == '[DONE]') {
                    break;
                }
                $result = json_decode($data, true);

                $choice       = $result['Choices'][0];
                $delta        = $choice['Delta'];
                $finishReason = $choice['FinishReason'] ?? null;

                if (!empty($delta['ToolCalls'])) {
                    $current = $delta['ToolCalls'][0];
                    if ($current['Type'] == 'function') {
                        if (!isset($current['Index'])) {
                            $current['Index'] = 0;
                        }
                        if ($call) {
                            if ($call['Index'] != $current['Index']) {
                                yield [
                                    'delta'         => [
                                        'role'       => 'assistant',
                                        'content'    => null,
                                        'tool_calls' => [
                                            [
                                                'id'       => $call['Id'],
                                                'index'    => $call['Index'],
                                                'type'     => $call['Type'],
                                                'function' => [
                                                    'name'      => $call['Function']['Name'],
                                                    'arguments' => $call['Function']['Arguments'],
                                                ],
                                            ],
                                        ],
                                    ],
                                    'finish_reason' => null,
                                ];
                                $call = $current;
                            } else {
                                $call['Function']['Arguments'] .= $current['Function']['Arguments'];
                            }
                        } else {
                            $call = $current;
                        }
                    } else {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [
                                    [
                                        'id'       => $current['Id'],
                                        'index'    => $current['Index'],
                                        'type'     => $current['Type'],
                                        'function' => [
                                            'name'      => $current['Function']['Name'],
                                            'arguments' => $current['Function']['Arguments'],
                                        ],
                                    ],
                                ],
                            ],
                            'finish_reason' => null,
                        ];
                    }
                } else {
                    if ($call) {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [
                                    [
                                        'id'       => $call['Id'],
                                        'index'    => $call['Index'],
                                        'type'     => $call['Type'],
                                        'function' => [
                                            'name'      => $call['Function']['Name'],
                                            'arguments' => $call['Function']['Arguments'],
                                        ],
                                    ],
                                ],
                            ],
                            'finish_reason' => null,
                        ];
                        $call = null;
                    }

                    if (strlen($delta['Content'] ?? '') > 0) {
                        yield [
                            'delta'         => [
                                'role'    => $delta['Role'],
                                'content' => $delta['Content'],
                            ],
                            'finish_reason' => null,
                        ];
                    }
                    if (strlen($delta['ReasoningContent'] ?? '') > 0) {
                        yield [
                            'delta'         => [
                                'role'      => $delta['Role'],
                                'reasoning' => $delta['ReasoningContent'],
                            ],
                            'finish_reason' => null,
                        ];
                    }
                }

                if (!empty($finishReason)) {
                    $usage = [
                        'prompt_tokens'     => $result['Usage']['PromptTokens'],
                        'completion_tokens' => $result['Usage']['CompletionTokens'],
                        'total_tokens'      => $result['Usage']['TotalTokens'],
                    ];
                    yield [
                        'usage'         => $this->applyFactor($usage),
                        'finish_reason' => $finishReason,
                    ];
                }
            }
        } else {
            $choice  = $res['Choices'][0];
            $message = [
                'role'    => $choice['Message']['Role'],
                'content' => $choice['Message']['Content'],
            ];

            if (!empty($choice['Message']['ReasoningContent'])) {
                $message['reasoning'] = $choice['Message']['ReasoningContent'];
            }

            if (!empty($choice['Message']['ToolCalls'])) {
                $message['tool_calls'] = array_map(function ($current) {
                    return [
                        'id'       => $current['Id'],
                        'index'    => $current['Index'],
                        'type'     => $current['Type'],
                        'function' => [
                            'name'      => $current['Function']['Name'],
                            'arguments' => $current['Function']['Arguments'],
                        ],
                    ];
                }, $choice['Message']['ToolCalls']);
            }

            $finishReason = $choice['FinishReason'];

            $usage = [
                'prompt_tokens'     => $res['Usage']['PromptTokens'],
                'completion_tokens' => $res['Usage']['CompletionTokens'],
                'total_tokens'      => $res['Usage']['TotalTokens'],
            ];

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }
}
