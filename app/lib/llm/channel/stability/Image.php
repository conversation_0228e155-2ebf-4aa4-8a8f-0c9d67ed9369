<?php

namespace app\lib\llm\channel\stability;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\Exception;
use app\lib\llm\traits\DefaultImage;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function getFeatures($model)
    {
        return [
            'sizes' => ['1024x1024', '768x1344', '1344x768'],
            'seed'  => true,
        ];
    }

    public function generations($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $prompt = $params['prompt'] ?? '';
        $size   = $params['size'] ?? '1024x1024';
        $format = $params['response_format'] ?? 'b64_json';

        if ($format != 'b64_json') {
            throw new Exception("not support format: {$format}");
        }

        $sizes = [
            '1024x1024' => '1:1',
            '1344x768'  => '16:9',
            '768x1344'  => '9:16',
            '1536x640'  => '21:9',
            '640x1536'  => '9:21',
            '832x1216'  => '2:3',
            '1216x832'  => '3:2',
            '896x1088'  => '4:5',
            '1088x896'  => '5:4',
        ];

        if (!array_key_exists($size, $sizes)) {
            throw new Exception("not support size: {$size}");
        }

        $data = [
            [
                'name'     => 'prompt',
                'contents' => $prompt,
            ],
            [
                'name'     => 'model',
                'contents' => $model,
            ],
        ];

        $refImg = $params['image']['url'] ?? null;
        if (!empty($refImg)) {
            $mode = 'image-to-image';

            if (str_starts_with($refImg, 'data:image/')) {
                $parts = explode(',', $refImg, 2);
                if (count($parts) == 2) {
                    $refImg = base64_decode($parts[1]);
                }
            } elseif (str_starts_with($refImg, 'http')) {
                $refImg = fopen($refImg, 'r');
            } else {
                throw new Exception("not support image url: {$refImg}");
            }

            $data[] = [
                'name'     => 'image',
                'contents' => $refImg,
                'filename' => 'image.png',
            ];

            $refStrength = $params['image']['strength'] ?? 0.5;

            $data[] = [
                'name'     => 'strength',
                'contents' => $refStrength,
            ];
        } else {
            $mode   = 'text-to-image';
            $data[] = [
                'name'     => 'aspect_ratio',
                'contents' => $sizes[$size],
            ];
        }

        $data[] = [
            'name'     => 'mode',
            'contents' => $mode,
        ];

        if (!empty($params['seed'])) {
            $data[] = [
                'name'     => 'seed',
                'contents' => $params['seed'],
            ];
        }

        $result = $this->request('stable-image/generate/sd3', [
            'multipart' => $data,
            'headers'   => [
                'accept' => 'application/json;type=image/png',
            ],
        ]);

        return [
            'images' => [
                [
                    'b64_json' => $result['image'],
                ],
            ],
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => 1,
                'completion_tokens' => 0,
                'total_tokens'      => 1,
            ]),
        ];
    }

    public function upscale($params)
    {
        $image  = $params['image'] ?? null;
        $format = $params['response_format'] ?? 'b64_json';

        if ($format != 'b64_json') {
            throw new Exception("not support format: {$format}");
        }

        $data = [
            [
                'name'     => 'image',
                'contents' => $this->getImageData($image, false),
                'filename' => 'image.png',
            ],
        ];

        $result = $this->request('stable-image/upscale/fast', [
            'multipart' => $data,
            'headers'   => [
                'accept' => 'application/json;type=image/png',
            ],
        ]);

        $usage = 0.25;

        return [
            'images' => [
                [
                    'b64_json' => $result['image'],
                ],
            ],
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }
}
