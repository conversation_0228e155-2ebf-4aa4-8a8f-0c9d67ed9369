<?php

namespace app\lib\llm\channel\stability;

use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use think\helper\Arr;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = 'Stability';

    protected function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;

        $result = json_decode($response->getBody()->getContents(), true);

        if (!$isOk) {
            throw new Exception(Arr::get($result, 'errors.0', 'Unknown error'));
        }

        return $result;
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://api.stability.ai/v2beta/'),
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth()}",
            ],
        ];
    }
}
