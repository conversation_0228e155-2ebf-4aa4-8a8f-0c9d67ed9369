<?php

namespace app\lib\llm\channel\deepseek;

use app\lib\llm\channel\OpenAiCompatibleResponse;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = 'DeepSeek';

    use OpenAiCompatibleResponse;

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://api.deepseek.com'),
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth()}",
            ],
        ];
    }
}
