<?php

namespace app\lib\llm\channel\volcengine;

use app\lib\llm\contract\MusicInterface;
use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use think\helper\Arr;
use think\helper\Str;

class Music extends Driver implements MusicInterface
{
    protected $region  = 'cn-beijing';
    protected $service = 'imagination';

    public function getFeatures($model)
    {
        return [
            'song' => match ($model) {
                default => [
                    'durations' => ['min' => 30, 'max' => 240],
                ]
            },
            'bgm'  => match ($model) {
                default => [
                    'durations' => ['min' => 1, 'max' => 60],
                ]
            },
        ];
    }

    public function song($params)
    {
        $duration = $params['duration'] ?? 30;

        $data = [
            'Lyrics'   => $params['lyrics'] ?? null,
            'Prompt'   => $params['prompt'] ?? '',
            'Duration' => $duration,
        ];

        $res = $this->request('/', [
            'query' => [
                'Action'  => 'GenSongForTime',
                'Version' => '2024-08-12',
            ],
            'json'  => $data,
        ]);

        $usage = $duration;

        return [
            'id'    => Arr::get($res, 'Result.TaskID'),
            'usage' => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function bgm($params)
    {
        $duration = $params['duration'] ?? 30;

        $data = [
            'Text'     => $params['prompt'] ?? '',
            'Duration' => $duration,
        ];

        $res = $this->request('/', [
            'query' => [
                'Action'  => 'GenBGMForTime',
                'Version' => '2024-08-12',
            ],
            'json'  => $data,
        ]);

        $usage = $duration;

        return [
            'id'    => Arr::get($res, 'Result.TaskID'),
            'usage' => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function query($params)
    {
        $result = $this->request('/', [
            'query' => [
                'Action'  => 'QuerySong',
                'Version' => '2024-08-12',
            ],
            'json'  => [
                'TaskID' => $params['id'] ?? null,
            ],
        ]);

        $status = match (Arr::get($result, 'Result.Status')) {
            0, 1 => 'processing',
            2 => 'success',
            default => 'fail',
        };

        if ($status == 'success') {
            $audios = [
                [
                    'url' => Arr::get($result, 'Result.SongDetail.AudioUrl'),
                ],
            ];
        }

        return [
            'status' => $status,
            'audios' => $audios ?? null,
        ];
    }

    protected function transformResponse(ResponseInterface $response)
    {
        $contentType = $response->getHeaderLine('Content-Type');

        if (Str::startsWith($contentType, 'text/event-stream')) {
            return $response->getBody();
        }

        $statusCode = $response->getStatusCode();
        $content    = $response->getBody()->getContents();
        $result     = json_decode($content, true);
        $isOk       = $statusCode == 200 && Arr::get($result, 'Code') == 0;

        if (!$isOk) {
            if ($result) {
                throw new Exception($result['ResponseMetadata']['Error']['Message'] ?? 'Unknown error');
            }
            throw new Exception('Unknown error');
        }

        return $result;
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => 'https://open.volcengineapi.com',
        ];
    }
}
