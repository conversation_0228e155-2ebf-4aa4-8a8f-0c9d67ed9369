<?php

namespace app\lib\llm\channel\volcengine;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\traits\DefaultImage;
use think\helper\Arr;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function getFeatures($model)
    {
        return match ($model) {
            'high_aes' => [
                'ref'   => true,
                'sizes' => [
                    '1024x1024', '768x1344', '1344x768',
                ],
                'seed'  => true,
            ],
            default => [
                'sizes' => [
                    '1024x1024',   // 1:1
                    '1024x768',    // 4:3
                    '768x1024',    // 3:4
                    '1024x682',    // 3:2
                    '682x1024',    // 2:3
                    '1024x576',    // 16:9
                    '576x1024',    // 9:16
                ],
                'seed'  => true,
            ]
        };
    }

    protected function getModelData($model, $params)
    {
        $data = [];

        switch ($model) {
            case 'high_aes':
                $data['model_version'] = 'anime_v1.3.1';

                $refImg = $params['image']['url'] ?? null;
                if (!empty($refImg)) {
                    $data['binary_data_base64'] = [$this->getImageData($refImg)];

                    $refStrength = $params['image']['strength'] ?? null;

                    if (!empty($refStrength)) {
                        $data['strength'] = $refStrength;
                    }
                }
                break;
        }
        return $data;
    }

    public function generations($params)
    {
        $model = $this->options['checkpoint'] ?? 'high_aes';

        $prompt = $params['prompt'] ?? '';
        $size   = $params['size'] ?? '1024x1024';
        $seed   = $params['seed'] ?? -1;
        $format = $params['response_format'] ?? 'url';

        [$width, $height] = explode('x', $size);

        $data = [
            'req_key'    => $model,
            'prompt'     => $prompt,
            'seed'       => $seed,
            'width'      => (int) $width,
            'height'     => (int) $height,
            ...$this->getModelData($model, $params),
            'return_url' => $format == 'url',
        ];

        $res = $this->request('/', [
            'query' => [
                'Action'  => 'CVProcess',
                'Version' => '2022-08-31',
            ],
            'json'  => $data,
        ]);

        if ($format == 'url') {
            $images = [
                [
                    'url' => Arr::get($res, 'data.image_urls.0'),
                ],
            ];
        } else {
            $images = [
                [
                    'b64_json' => Arr::get($res, 'data.binary_data_base64.0'),
                ],
            ];
        }

        return [
            'images' => $images,
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => 1,
                'completion_tokens' => 0,
                'total_tokens'      => 1,
            ]),
        ];
    }

    public function inpainting($params)
    {
        $prompt = $params['prompt'] ?? '';
        $image  = $params['image'] ?? null;
        $mask   = $params['mask'] ?? null;
        $format = $params['response_format'] ?? 'url';

        $data = [
            'req_key'            => empty($prompt) ? 'i2i_inpainting' : 'i2i_inpainting_edit',
            'return_url'         => $format == 'url',
            'binary_data_base64' => [$this->getImageData($image), $this->getImageData($mask)],
        ];

        if (!empty($prompt)) {
            $data['custom_prompt'] = $prompt;
        }

        $res = $this->request('/', [
            'query' => [
                'Action'  => empty($prompt) ? 'Img2ImgInpainting' : 'Img2ImgInpaintingEdit',
                'Version' => '2022-08-31',
            ],
            'json'  => $data,
        ]);

        $usage = 1;

        if ($format == 'url') {
            $images = [
                [
                    'url' => Arr::get($res, 'data.image_urls.0'),
                ],
            ];
        } else {
            $images = [
                [
                    'b64_json' => Arr::get($res, 'data.binary_data_base64.0'),
                ],
            ];
        }

        return [
            'images' => $images,
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function outpainting($params)
    {
        $image  = $params['image'] ?? null;
        $prompt = $params['prompt'] ?? '';
        $top    = $params['top'] ?? null;
        $bottom = $params['bottom'] ?? null;
        $left   = $params['left'] ?? null;
        $right  = $params['right'] ?? null;
        $format = $params['response_format'] ?? 'url';

        $data = [
            'req_key'            => 'i2i_outpainting',
            'return_url'         => $format == 'url',
            'binary_data_base64' => [$this->getImageData($image)],
            'custom_prompt'      => $prompt,
            'top'                => $top,
            'bottom'             => $bottom,
            'left'               => $left,
            'right'              => $right,
        ];

        $res = $this->request('/', [
            'query' => [
                'Action'  => 'Img2ImgOutpainting',
                'Version' => '2022-08-31',
            ],
            'json'  => array_filter_null($data),
        ]);

        $usage = 1;

        if ($format == 'url') {
            $images = [
                [
                    'url' => Arr::get($res, 'data.image_urls.0'),
                ],
            ];
        } else {
            $images = [
                [
                    'b64_json' => Arr::get($res, 'data.binary_data_base64.0'),
                ],
            ];
        }

        return [
            'images' => $images,
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function upscale($params)
    {
        $image  = $params['image'] ?? null;
        $format = $params['response_format'] ?? 'url';

        $data = [
            'req_key'             => 'lens_lqir',
            'binary_data_base64'  => [$this->getImageData($image)],
            'resolution_boundary' => '2k',
            'return_url'          => $format == 'url',
        ];

        $res = $this->request('/', [
            'query' => [
                'Action'  => 'EnhancePhotoV2',
                'Version' => '2022-08-31',
            ],
            'json'  => array_filter_null($data),
        ]);

        $usage = 1;

        if ($format == 'url') {
            $images = [
                [
                    'url' => Arr::get($res, 'data.image_urls.0'),
                ],
            ];
        } else {
            $images = [
                [
                    'b64_json' => Arr::get($res, 'data.binary_data_base64.0'),
                ],
            ];
        }

        return [
            'images' => $images,
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function edit($params)
    {
        $prompt = $params['prompt'] ?? '';
        $image  = $params['image'] ?? null;
        $format = $params['response_format'] ?? 'url';

        $data = [
            'req_key'            => 'byteedit_v2.0',
            'return_url'         => $format == 'url',
            'binary_data_base64' => [$this->getImageData($image)],
            'prompt'             => $prompt,
        ];

        $res = $this->request('/', [
            'query' => [
                'Action'  => 'CVProcess',
                'Version' => '2022-08-31',
            ],
            'json'  => $data,
        ]);

        if ($format == 'url') {
            $images = [
                [
                    'url' => Arr::get($res, 'data.image_urls.0'),
                ],
            ];
        } else {
            $images = [
                [
                    'b64_json' => Arr::get($res, 'data.binary_data_base64.0'),
                ],
            ];
        }

        return [
            'images' => $images,
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => 1,
                'completion_tokens' => 0,
                'total_tokens'      => 1,
            ]),
        ];
    }

}
