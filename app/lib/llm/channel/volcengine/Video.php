<?php

namespace app\lib\llm\channel\volcengine;

use app\lib\llm\contract\VideoInterface;
use think\helper\Arr;

class Video extends Driver implements VideoInterface
{
    public function getFeatures($model)
    {
        return match ($model) {
            'jimeng_vgfm_t2v_l20' => [
                'text'      => true,
                'durations' => [5],
                'ratios'    => ['16:9', '9:16', '1:1', '4:3', '3:4', '21:9'],
            ],
            'jimeng_vgfm_i2v_l20' => [
                'image'     => true,
                'durations' => [5],
                'ratios'    => ['16:9', '9:16', '1:1', '4:3', '3:4', '21:9'],
            ],
            default => [],
        };
    }

    protected function transformData(&$data, $model, $params)
    {
        switch ($model) {
            case 'jimeng_vgfm_i2v_l20':
                $data['binary_data_base64'] = [$this->getImageData($params['image'] ?? null)];
                break;
        }
    }

    public function generations($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $prompt = $params['prompt'] ?? '';

        $data = [
            'req_key'      => $model,
            'prompt'       => $prompt,
            'aspect_ratio' => $params['ratio'] ?? '16:9',
        ];

        $this->transformData($data, $model, $params);

        $res = $this->request('/', [
            'query' => [
                'Action'  => 'CVSync2AsyncSubmitTask',
                'Version' => '2022-08-31',
            ],
            'json'  => $data,
        ]);

        $usage = 5;

        return [
            'id'    => "{$model}@" . Arr::get($res, 'data.task_id'),
            'usage' => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function query($params)
    {
        [$model, $taskId] = explode('@', $params['id']);

        $result = $this->request('/', [
            'query' => [
                'Action'  => 'CVSync2AsyncGetResult',
                'Version' => '2022-08-31',
            ],
            'json'  => [
                'req_key' => $model,
                'task_id' => $taskId,
            ],
        ]);

        //TODO 异常时的失败处理

        $status = match (Arr::get($result, 'data.status')) {
            'in_queue' => 'processing',
            'done' => 'success',
            default => 'fail',
        };

        if ($status == 'success') {
            $videos = [
                [
                    'url' => Arr::get($result, 'data.video_url'),
                ],
            ];
        }

        return [
            'status' => $status,
            'videos' => $videos ?? null,
        ];
    }
}
