<?php

namespace app\lib\llm\channel\volcengine;

use app\lib\llm\Exception;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use think\helper\Str;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = '火山智能';

    protected $region  = 'cn-north-1';
    protected $service = 'cv';

    protected function transformResponse(ResponseInterface $response)
    {
        $contentType = $response->getHeaderLine('Content-Type');

        if (Str::startsWith($contentType, 'text/event-stream')) {
            return $response->getBody();
        }

        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;
        $content    = $response->getBody()->getContents();
        $result     = json_decode($content, true);

        if (!$isOk) {
            if ($result) {
                throw new Exception($result['message'] ?? $result['ResponseMetadata']['Error']['Message'] ?? 'Unknown error');
            }
            throw new Exception('Unknown error');
        }

        return $result;
    }

    protected function transformRequest(RequestInterface $request)
    {
        $method = $request->getMethod();
        $url    = (string) $request->getUri();
        $body   = $request->getBody()->getContents();

        $date      = gmdate('Ymd\THis\Z');
        $shortDate = substr($date, 0, 8);
        $bodyHex   = hash('sha256', $body);
        $path      = parse_url($url, PHP_URL_PATH);
        $query     = parse_url($url, PHP_URL_QUERY);

        $signHeaders = [
            'Host'             => $request->getHeaderLine('Host'),
            'Content-Type'     => $request->getHeaderLine('Content-Type'),
            'X-Content-Sha256' => $bodyHex,
            'X-Date'           => $date,
        ];

        $signedHeaderStr     = join(';', ['content-type', 'host']);
        $canonicalRequestStr = join("\n", [
            $method,
            $path,
            $query,
            join("\n", ['content-type:' . $signHeaders['Content-Type'], 'host:' . $signHeaders['Host']]),
            '',
            $signedHeaderStr,
            $bodyHex,
        ]);

        $hashedCanonicalRequest = hash('sha256', $canonicalRequestStr);
        $credentialScope        = join('/', [$shortDate, $this->region, $this->service, 'request']);
        $stringToSign           = join("\n", ['HMAC-SHA256', $date, $credentialScope, $hashedCanonicalRequest]);

        $kDate     = hash_hmac('sha256', $shortDate, $this->getAuth('secret_key'), true);
        $kRegion   = hash_hmac('sha256', $this->region, $kDate, true);
        $kService  = hash_hmac('sha256', $this->service, $kRegion, true);
        $kSigning  = hash_hmac('sha256', 'request', $kService, true);
        $signature = hash_hmac('sha256', $stringToSign, $kSigning);

        $authValue = sprintf('HMAC-SHA256 Credential=%s, SignedHeaders=%s, Signature=%s', $this->getAuth('access_key') . '/' . $credentialScope, $signedHeaderStr, $signature);

        return $request
            ->withHeader('X-Date', $date)
            ->withHeader('X-Content-Sha256', $bodyHex)
            ->withHeader('Authorization', $authValue);
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => 'https://visual.volcengineapi.com',
        ];
    }
}
