<?php

namespace app\lib\llm\channel\glm;

use app\lib\llm\contract\VideoInterface;
use think\helper\Arr;

class Video extends Driver implements VideoInterface
{

    public function getFeatures($model)
    {
        return match ($model) {
            'cogvideox-2' => [
                'image'     => true,
                'text'      => true,
                'sizes'     => ['720x480', '1024x1024', '1280x960', '960x1280', '1920x1080', '1080x1920', '2048x1080', '3840x2160'],
                'durations' => [5],
            ],
            'viduq1-text' => [
                'text'      => true,
                'ratios'    => ['16:9', '9:16', '1:1'],
                'durations' => [5],
            ],
            'viduq1-image', => [
                'image'     => true,
                'durations' => [5],
            ],
            'vidu2-image' => [
                'image'     => true,
                'durations' => [4],
            ],
            'viduq1-start-end' => [
                'frame'     => true,
                'durations' => [5],
            ],
            'vidu2-start-end' => [
                'frame'     => true,
                'durations' => [4],
            ],
            'vidu2-reference' => [
                'ref'       => true,
                'ratios'    => ['16:9', '9:16', '1:1'],
                'durations' => [5],
            ],
            default => []
        };
    }

    protected function transformData(&$data, $model, $params)
    {
        switch ($model) {
            case 'cogvideox-2':
                $data['with_audio'] = true;
                $data['quality']    = ($params['quality'] ?? 'hd') == 'hd' ? 'quality' : 'speed';
                $data['image_url']  = $this->getImageUrl($params['image'] ?? null);
                $data['size']       = $params['sizes'] ?? '1920x1080';
                break;
            case 'viduq1-text':
                $data['aspect_ratio'] = $params['ratio'] ?? '16:9';
                break;
            case 'viduq1-image':
            case 'vidu2-image':
            case 'viduq1-start-end':
            case 'vidu2-start-end':
                $data['image_url'] = $this->getImageUrl($params['image'] ?? null);
                break;
            case 'vidu2-reference':
                $data['image_url']    = $this->getImageUrl($params['image'] ?? null);
                $data['aspect_ratio'] = $params['ratio'] ?? '16:9';
                break;
        }
    }

    public function generations($params)
    {
        $model = $this->options['checkpoint'] ?? 'cogvideox-2';

        $prompt   = $params['prompt'] ?? '';
        $duration = $params['duration'] ?? 5;

        $data = [
            'model'  => $model,
            'prompt' => $prompt,
        ];

        $this->transformData($data, $model, $params);

        $result = $this->request('videos/generations', [
            'json' => array_filter_null($data),
        ]);

        $usage = $duration;

        return [
            'id'    => Arr::get($result, 'id'),
            'usage' => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function query($params)
    {
        $result = $this->request("async-result/{$params['id']}", [], 'GET');

        $status = match ($result['task_status']) {
            'PROCESSING' => 'processing',
            'SUCCESS' => 'success',
            'FAIL' => 'fail',
        };

        if ($status == 'success') {
            $videos = array_map(function ($result) {
                return [
                    'url'   => Arr::get($result, 'url'),
                    'cover' => Arr::get($result, 'cover_image_url'),
                ];
            }, Arr::get($result, 'video_result', []));
        }

        return [
            'status' => $status,
            'videos' => $videos ?? null,
            'error'  => null,
        ];
    }
}
