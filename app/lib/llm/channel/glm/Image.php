<?php

namespace app\lib\llm\channel\glm;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\Exception;
use app\lib\llm\traits\DefaultImage;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function generations($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $prompt = $params['prompt'] ?? '';
        $size   = $params['size'] ?? '1024x1024';
        $format = $params['response_format'] ?? 'url';

        if ($format != 'url') {
            throw new Exception("not support format: {$format}");
        }

        $result = $this->request('images/generations', [
            'json' => [
                'model'  => $model,
                'prompt' => $prompt,
                'size'   => $size,
            ],
        ]);

        $usage = 1;

        return [
            'images' => $result['data'],
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }
}
