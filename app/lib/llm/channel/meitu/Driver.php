<?php

namespace app\lib\llm\channel\meitu;

use app\lib\Date;
use app\lib\llm\Exception;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = '美图';

    protected function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;

        $result = json_decode($response->getBody()->getContents(), true);

        if (!$isOk) {
            if (!empty($result['message'])) {
                $message = $result['message'];
                if (str_starts_with($message, '{')) {
                    $subResult = json_decode($message, true);
                    if (!empty($subResult['message'])) {
                        $message = $subResult['message'];
                    }
                }
            }

            throw new Exception($message ?? $result['ErrorMsg'] ?? 'Unknown error');
        }

        return $result['data'];
    }

    protected function canonicalHeaders($headers, $signedHeaders)
    {
        $lowheaders = [];
        foreach ($headers as $key => $value) {
            $lowheaders[strtolower($key)] = trim(implode(', ', $value));
        }
        $a = [];
        foreach ($signedHeaders as $key) {
            array_push($a, $key . ':' . $lowheaders[$key]);
        }
        return join("\n", $a);
    }

    protected function canonicalRequest($method, $url, $headers, $body, $signedHeaders)
    {
        $canonicalURI         = parse_url($url, PHP_URL_PATH) . '/';
        $canonicalQueryString = parse_url($url, PHP_URL_QUERY);
        $canonicalHeaders     = $this->canonicalHeaders($headers, $signedHeaders);
        $signedHeadersStr     = implode(';', $signedHeaders);

        $hexencode = hash('sha256', $body);

        return sprintf("%s\n%s\n%s\n%s\n%s\n%s",
            $method, $canonicalURI, $canonicalQueryString, $canonicalHeaders,
            $signedHeadersStr, $hexencode);
    }

    protected function signedHeaders($headers)
    {
        $signedHeaders = [];
        foreach ($headers as $header => $value) {
            $signedHeaders[] = strtolower($header);
        }
        sort($signedHeaders);
        return $signedHeaders;
    }

    protected function stringToSign($canonicalRequest, $timeFormat)
    {
        $hash = hash('sha256', $canonicalRequest, true);
        return sprintf("%s\n%s\n%s",
            'SDK-HMAC-SHA256', $timeFormat, bin2hex($hash));
    }

    protected function signStringToSign($stringToSign, $signingKey)
    {
        $hm = hash_hmac('sha256', $stringToSign, $signingKey, true);
        return bin2hex($hm);
    }

    protected function authHeaderValue($signature, $accessKey, $signedHeaders)
    {
        $signedHeadersStr = implode(';', $signedHeaders);
        $headerValue      = sprintf('%s Access=%s, SignedHeaders=%s, Signature=%s', 'SDK-HMAC-SHA256', $accessKey, $signedHeadersStr, $signature);
        $encodeVal        = base64_encode($headerValue);

        return 'Bearer ' . $encodeVal;
    }

    protected function transformRequest(RequestInterface $request)
    {
        $date    = Date::now('UTC')->format('Ymd\THis\Z');
        $request = $request->withHeader('X-Sdk-Date', $date);
        $headers = $request->getHeaders();
        $method  = $request->getMethod();
        $url     = (string) $request->getUri();
        $body    = $request->getBody()->getContents();

        $signedHeaders    = $this->signedHeaders($headers);
        $canonicalRequest = $this->canonicalRequest($method, $url, $headers, $body, $signedHeaders);
        $stringToSign     = $this->stringToSign($canonicalRequest, $date);
        $signature        = $this->signStringToSign($stringToSign, $this->getAuth('secret_key'));
        $authValue        = $this->authHeaderValue($signature, $this->getAuth('access_key'), $signedHeaders);

        return $request->withHeader('Authorization', $authValue);
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://openapi.meitu.com'),
        ];
    }
}
