<?php

namespace app\lib\llm\channel\meitu;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\Exception;
use app\lib\llm\traits\DefaultImage;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function getFeatures($model)
    {
        return [
            'n'      => 4,
            'ref'    => true,
            'seed'   => true,
            'sizes'  => [
                '1024x1024',
                '768x1152',
                '768x1024',
                '720x1280',
                '1152x768',
                '1024x768',
                '1280x720',
            ],
            'styles' => [
                [
                    'name' => '智能推荐',
                    'code' => null,
                ],
                [
                    'name' => '好莱坞',
                    'code' => '54a69e7b9024971b02c50f768a26ed86_1.0',
                ],
                [
                    'name' => '文艺电影',
                    'code' => '12bc2a0a0bbdf528da20f9a943271cbc_1.0',
                ],
                [
                    'name' => '国漫',
                    'code' => '76caf5621a01fdb45c4ecf3ba4f06c0c_1.0',
                ],
                [
                    'name' => '东方插画',
                    'code' => 'dongfangchahua-v1_1.0',
                ],
                [
                    'name' => '线条图形',
                    'code' => '02cd3a8ce711ec557fbcd74506ab6fab_1.0',
                ],
                [
                    'name' => '极简主义',
                    'code' => '2970c9b970ee42e9356f635a4edc8ff1_1.0',
                ],
                [
                    'name' => '概念设计',
                    'code' => '29171ba5e434b6f140e349cb7c76fc37_1.0',
                ],
                [
                    'name' => '未来科幻',
                    'code' => 'scifi-v1_1.0',
                ],
                [
                    'name' => '动漫',
                    'code' => 'wallhaven0823v1_v1.0',
                ],
                [
                    'name' => '艺术大师',
                    'code' => 'c8ea1bee0e151a8cb02e73c0d6aca413_1.0',
                ],
                [
                    'name' => '像素风',
                    'code' => '15635d63df919198507f9fcc52ebeb94_1.0',
                ],
                [
                    'name' => '3D卡通',
                    'code' => '9c943a3b6c4dcffe1f08b2474a0d6fee_1.0',
                ],
                [
                    'name' => '中式玄幻',
                    'code' => '50db365b4a55e545359fa1f5dc707912_1.0',
                ],
                [
                    'name' => '类定格',
                    'code' => '7c0f3e07cb0de784cb7d04293ebf97c9_1.0',
                ],
                [
                    'name' => '赛级风光',
                    'code' => 'landscapephotographyoptimization-v1_1.0',
                ],
            ],
        ];
    }

    public function generations($params)
    {
        $prompt = $params['prompt'] ?? '';
        $size   = $params['size'] ?? '1024x1024';
        $n      = $params['n'] ?? 1;
        $format = $params['response_format'] ?? 'url';

        if ($format != 'url') {
            throw new Exception("not support format: {$format}");
        }

        $style = $params['style'] ?? null;
        if (!empty($style)) {
            $prompt .= ",<lora:{$style}:0.8>";
        }

        [$width, $height] = explode('x', $size);

        $data = [
            'params'    => [
                'model'      => 'miracle-vision_v3.1-realistic1.zip',
                'prompt'     => $prompt,
                'width'      => (int) $width,
                'height'     => (int) $height,
                'batch_size' => $n,
            ],
            'task_type' => 'inference',
        ];

        $refImg = $params['image']['url'] ?? null;
        if (!empty($refImg)) {
            $data['init_images'] = [
                ['url' => $refImg],
            ];

            $data['task'] = 'img2img';

            $refStrength = $params['image']['strength'] ?? null;
            if (!empty($refStrength)) {
                $data['params']['denoising_strength'] = $refStrength;
            }

            $uri = 'whee/business/image2image.json';
        } else {
            $data['task'] = 'txt2img';

            $uri = 'whee/business/text2image.json';
        }

        if (!empty($params['seed'])) {
            $data['params']['seed'] = $params['seed'];
        }

        $data['params'] = json_encode($data['params']);

        $res = $this->request($uri, [
            'json' => $data,
        ]);

        if ($res['status'] != 10) {
            $waited = 0;
            while (true) {
                $res = $this->queryTaskStatus($res['result']['id']);

                if ($res['status'] == -1 || $res['status'] == 2) {
                    throw new Exception('task failed');
                }

                if ($res['status'] == 10) {
                    break;
                }

                if ($waited > 120) {
                    throw new Exception('task timeout');
                }
                $waited += 3;
                sleep(3);
            }
        }

        return [
            'images' => array_map(function ($url) {
                return ['url' => $url];
            }, $res['result']['urls']),
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => 1,
                'completion_tokens' => 0,
                'total_tokens'      => 1,
            ]),
        ];
    }

    protected function queryTaskStatus($id)
    {
        return $this->request('api/v1/sdk/status', [
            'query' => [
                'task_id' => $id,
            ],
        ], 'GET');
    }
}
