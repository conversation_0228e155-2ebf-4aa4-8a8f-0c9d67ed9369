<?php

namespace app\lib\llm\channel\qianfan;

use app\lib\llm\channel\OpenAiCompatibleResponse;
use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use think\helper\Str;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = '千帆';

    use OpenAiCompatibleResponse;

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://qianfan.baidubce.com'),
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth()}",
            ],
        ];
    }
}
