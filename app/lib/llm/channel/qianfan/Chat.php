<?php

namespace app\lib\llm\channel\qianfan;

use app\lib\llm\contract\ChatInterface;
use Psr\Http\Message\StreamInterface;

class Chat extends Driver implements ChatInterface
{
    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $messages = $params['messages'] ?? [];
        $tools    = $params['tools'] ?? null;
        $stream   = $params['stream'] ?? true;

        $json = [
            'model'           => $model,
            'messages'        => $messages,
            'stream'          => $stream,
            'stream_options'  => $stream ? [
                'include_usage' => true,
            ] : null,
            'tools'           => $tools,
            'temperature'     => $params['temperature'] ?? null,
            'max_tokens'      => $params['max_tokens'] ?? null,
            'seed'            => $params['seed'] ?? null,
            'response_format' => $params['response_format'] ?? null,
        ];

        $thinking = $this->getThinking($params);
        if ($thinking) {
            $json['enable_thinking'] = $thinking == 'enabled';
        }

        $res = $this->request('/v2/chat/completions', [
            'json'   => array_filter_null($json),
            'stream' => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $call         = null;
            $usage        = null;
            $finishReason = null;

            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];
                if ($data == '[DONE]') {
                    break;
                }

                $result = json_decode($data, true);
                $delta  = $result['choices'][0]['delta'] ?? null;
                if (!empty($result['usage'])) {
                    $usage = $result['usage'];
                }
                if (!empty($result['choices'][0]['finish_reason'])) {
                    $finishReason = $result['choices'][0]['finish_reason'];
                }

                if (!empty($delta)) {
                    //检查是否为tools_call
                    if (!empty($delta['tool_calls'])) {
                        $current = $delta['tool_calls'][0];
                        if ($call) {
                            if ($call['index'] != $current['index']) {
                                yield [
                                    'delta'         => [
                                        'role'       => 'assistant',
                                        'content'    => null,
                                        'tool_calls' => [$call],
                                    ],
                                    'finish_reason' => null,
                                ];
                                $call = $current;
                            } else {
                                $call['function']['arguments'] .= $current['function']['arguments'];
                            }
                        } else {
                            $call = $current;
                        }
                    } else {
                        if (isset($delta['reasoning_content'])) {
                            $delta['reasoning'] = $delta['reasoning_content'];
                            unset($delta['reasoning_content']);
                        }
                        yield [
                            'delta'         => $delta,
                            'finish_reason' => null,
                        ];
                    }
                }
            }

            if ($call) {
                yield [
                    'delta'         => [
                        'role'       => 'assistant',
                        'content'    => null,
                        'tool_calls' => [$call],
                    ],
                    'finish_reason' => null,
                ];
                $call = null;
            }

            yield [
                'usage'         => $this->applyFactor($usage),
                'finish_reason' => $finishReason ?? 'stop',
            ];
        } else {
            $message      = $res['choices'][0]['message'];
            $finishReason = $res['choices'][0]['finish_reason'];
            $usage        = $res['usage'];

            if (!empty($message['reasoning_content'])) {
                $message['reasoning'] = $message['reasoning_content'];
                unset($message['reasoning_content']);
            }

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }
}
