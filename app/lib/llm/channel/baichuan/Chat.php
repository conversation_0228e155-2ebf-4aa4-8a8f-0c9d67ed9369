<?php

namespace app\lib\llm\channel\baichuan;

use app\lib\llm\contract\ChatInterface;
use Psr\Http\Message\StreamInterface;

class Chat extends Driver implements ChatInterface
{

    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $messages    = $params['messages'] ?? [];
        $tools       = $params['tools'] ?? null;
        $stream      = $params['stream'] ?? true;
        $temperature = $params['temperature'] ?? null;

        if (!empty($tools)) {
            $tools = array_map(function ($item) {
                if ($item['type'] == 'function') {
                    if (!empty($item['function']['parameters']['properties'])) {
                        $item['function']['parameters']['properties'] = array_map(function ($item) {
                            if (!empty($item['enum'])) {
                                $item['description'] .= "\n\n枚举值：" . implode(',', $item['enum']);
                                unset($item['enum']);
                            }
                            return $item;
                        }, $item['function']['parameters']['properties']);
                    }
                }
                return $item;
            }, $tools);
        }

        $res = $this->request('chat/completions', [
            'json'   => [
                'model'           => $model,
                'messages'        => $messages,
                'stream'          => $stream,
                'tools'           => $tools,
                'temperature'     => $temperature,
                'max_tokens'      => $params['max_tokens'] ?? null,
                'response_format' => $params['response_format'] ?? null,
            ],
            'stream' => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $call = null;
            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];
                if ($data == '[DONE]') {
                    break;
                }

                $result = json_decode($data, true);

                $delta        = $result['choices'][0]['delta'];
                $finishReason = $result['choices'][0]['finish_reason'] ?? null;

                //检查是否为tools_call
                if (!empty($delta['tool_calls'])) {
                    $current = $delta['tool_calls'][0];
                    if ($call) {
                        if ($call['index'] != $current['index']) {
                            yield [
                                'delta'         => [
                                    'role'       => 'assistant',
                                    'content'    => null,
                                    'tool_calls' => [$call],
                                ],
                                'finish_reason' => null,
                            ];
                            $call = $current;
                        } else {
                            $call['function']['arguments'] .= $current['function']['arguments'];
                        }
                    } else {
                        $call = $current;
                    }
                }

                if (strlen($delta['content'] ?? '') > 0) {
                    yield [
                        'delta'         => $delta,
                        'finish_reason' => null,
                    ];
                }

                if (!empty($finishReason)) {
                    //最后一个delta是和finish_reason一起返回的
                    if ($call) {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [$call],
                            ],
                            'finish_reason' => null,
                        ];
                        $call = null;
                    }

                    yield [
                        'usage'         => $this->applyFactor($result['usage'] ?? null),
                        'finish_reason' => $finishReason,
                    ];
                }
            }
        } else {
            $message      = $res['choices'][0]['message'];
            $finishReason = $res['choices'][0]['finish_reason'];
            $usage        = $res['usage'] ?? null;

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }
}
