<?php

namespace app\lib\llm\channel\baichuan;

use app\lib\llm\channel\OpenAiCompatibleResponse;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = '百川';

    use OpenAiCompatibleResponse;

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://api.baichuan-ai.com/v1/'),
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth()}",
            ],
        ];
    }
}
