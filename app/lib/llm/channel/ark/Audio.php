<?php

namespace app\lib\llm\channel\ark;

use app\lib\llm\contract\AudioInterface;
use app\lib\llm\Exception;
use app\lib\llm\traits\DefaultAudio;
use Psr\Http\Message\RequestInterface;
use Psr\Http\Message\ResponseInterface;
use think\helper\Arr;

class Audio extends Driver implements AudioInterface
{
    use DefaultAudio;

    public function speech($params)
    {
        $input = $params['input'] ?? '';
        $voice = $params['voice'] ?? '';
        $speed = $params['speed'] ?? 1;

        $response = $this->request('/api/v1/tts', [
            'json'    => [
                'app'     => [
                    'appid'   => '2552392076',
                    'token'   => 'XgbxLCdehP871HTELoxalyGLa9Y2mPXg',
                    'cluster' => 'volcano_tts',
                ],
                'user'    => [
                    'uid' => uniqid(),
                ],
                'audio'   => [
                    'voice_type'  => $voice,
                    'encoding'    => 'mp3',
                    'speed_ratio' => $speed,
                ],
                'request' => [
                    'reqid'     => uniqid(),
                    'text'      => $input,
                    'operation' => 'query',
                ],
            ],
            'headers' => [
                'Authorization' => "Bearer;XgbxLCdehP871HTELoxalyGLa9Y2mPXg",
            ],
        ]);

        $content = $response->getBody()->getContents();
        $result  = json_decode($content, true);

        $usage = mb_strlen($input);

        return [
            'audio' => [
                'type' => "audio/mpeg",
                'data' => $result['data'],
            ],
            'usage' => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function transcriptions($params)
    {
        $taskId = uniqid();
        $this->request('/api/v3/auc/bigmodel/submit', [
            'json'    => [
                'audio'   => [
                    'url' => $params['url'],
                ],
                'request' => [
                    'model_name' => 'bigmodel',
                ],
            ],
            'headers' => [
                'X-Api-Request-Id' => $taskId,
                'X-Api-Sequence'   => -1,
            ],
        ]);

        usleep(500000);
        $waited = 0;
        //获取任务结果
        while (true) {
            $response = $this->request("/api/v3/auc/bigmodel/query", [
                'json'    => [
                    'X-Api-Request-Id' => $taskId,
                ],
                'headers' => [
                    'X-Api-Request-Id' => $taskId,
                ],
            ]);

            $code = $response->getHeaderLine('X-Api-Status-Code');

            if ($code == '20000000') {
                $content = $response->getBody()->getContents();
                $result  = json_decode($content, true);
                break;
            } elseif ($code != '20000001' && $code != '20000002') {
                throw new Exception($response->getHeaderLine('X-Api-Message') ?: 'transcriptions failed');
            }

            if ($waited > 15) {
                throw new Exception('task timeout');
            }
            $waited += .5;
            usleep(500000);
        }

        $duration = Arr::get($result, 'audio_info.duration', 0) / 1000;

        return [
            'text'  => Arr::get($result, 'result.text', ''),
            'usage' => $this->applyFactor([
                'prompt_tokens'     => 0,
                'completion_tokens' => $duration,
                'total_tokens'      => $duration,
            ]),
        ];
    }

    protected function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;
        if (!$isOk) {
            $content = $response->getBody()->getContents();
            $result  = json_decode($content, true);

            if ($result) {
                throw new Exception($result['message'] ?? $result['header']['message'] ?? 'Unknown error');
            }
            throw new Exception('Unknown error');
        }
        return $response;
    }

    protected function transformRequest(RequestInterface $request)
    {
        return $request
            ->withHeader('X-Api-App-Key', '2552392076')
            ->withHeader('X-Api-Access-Key', 'XgbxLCdehP871HTELoxalyGLa9Y2mPXg')
            ->withHeader('X-Api-Resource-Id', 'volc.bigasr.auc');
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => 'https://openspeech.bytedance.com',
        ];
    }
}
