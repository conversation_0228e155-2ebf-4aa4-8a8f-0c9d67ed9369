<?php

namespace app\lib\llm\channel\ark;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\traits\DefaultImage;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function getFeatures($model)
    {
        return match ($model) {
            'doubao-seedream-3-0-t2i-250415' => [
                'sizes' => ['1024x1024', '864x1152', '1152x864', '1280x720', '720x1280', '832x1248', '1248x832', '1512x648'],
                'seed'  => true,
            ],
            default => []
        };
    }

    public function generations($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $prompt = $params['prompt'] ?? '';
        $size   = $params['size'] ?? '1024x1024';
        $format = $params['response_format'] ?? 'url';

        $res = $this->request('images/generations', [
            'json' => [
                'model'           => $model,
                'prompt'          => $prompt,
                'response_format' => $format,
                'size'            => $size,
                'seed'            => $params['seed'] ?? null,
                'watermark'       => false,
            ],
        ]);

        return [
            'images' => $res['data'],
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => 1,
                'completion_tokens' => 0,
                'total_tokens'      => 1,
            ]),
        ];
    }
}
