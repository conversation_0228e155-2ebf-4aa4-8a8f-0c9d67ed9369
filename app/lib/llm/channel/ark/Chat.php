<?php

namespace app\lib\llm\channel\ark;

use app\lib\llm\contract\ChatInterface;
use Psr\Http\Message\StreamInterface;

class Chat extends Driver implements ChatInterface
{

    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $messages    = $params['messages'] ?? [];
        $tools       = $params['tools'] ?? [];
        $stream      = $params['stream'] ?? true;
        $temperature = $params['temperature'] ?? null;

        $json = [
            'model'           => $model,
            'messages'        => $messages,
            'tools'           => $tools,
            'temperature'     => $temperature,
            'stream'          => $stream,
            'stream_options'  => [
                'include_usage' => true,
            ],
            'response_format' => $params['response_format'] ?? null,
        ];

        $thinking = $this->getThinking($params);
        if ($thinking) {
            $json['thinking'] = [
                'type' => $thinking,
            ];
        }

        $res = $this->request("chat/completions", [
            'json'   => $json,
            'stream' => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $call         = null;
            $finishReason = null;
            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];

                if ($data == '[DONE]') {
                    break;
                }

                $result = json_decode($data, true);

                $delta        = $result['choices'][0]['delta'] ?? null;
                $usage        = $result['usage'] ?? null;
                $finishReason = $result['choices'][0]['finish_reason'] ?? $finishReason;

                //检查是否为tools_call
                if (!empty($delta['tool_calls'])) {
                    $current = $delta['tool_calls'][0];
                    if ($call) {
                        if ($call['index'] != $current['index']) {
                            yield [
                                'delta'         => [
                                    'role'       => 'assistant',
                                    'content'    => null,
                                    'tool_calls' => [$call],
                                ],
                                'finish_reason' => null,
                            ];
                            $call = $current;
                        } else {
                            $call['function']['arguments'] .= $current['function']['arguments'];
                        }
                    } else {
                        $call = $current;
                    }
                } else {
                    if ($call) {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [$call],
                            ],
                            'finish_reason' => null,
                        ];
                        $call = null;
                    }
                    if (strlen($delta['content'] ?? '') > 0 || strlen($delta['reasoning_content'] ?? '') > 0) {
                        if (isset($delta['reasoning_content'])) {
                            $delta['reasoning'] = $delta['reasoning_content'];
                            unset($delta['reasoning_content']);
                        }
                        yield [
                            'delta'         => $delta,
                            'finish_reason' => null,
                        ];
                    }

                    if (!empty($finishReason) && !empty($usage)) {

                        yield [
                            'usage'         => $this->applyFactor($usage),
                            'finish_reason' => $finishReason,
                        ];
                    }
                }
            }
        } else {
            $message      = $res['choices'][0]['message'];
            $finishReason = $res['choices'][0]['finish_reason'];
            $usage        = $res['usage'];

            if (!empty($message['reasoning_content'])) {
                $message['reasoning'] = $message['reasoning_content'];
                unset($message['reasoning_content']);
            }

            return [
                'message'       => $message,
                'finish_reason' => $finishReason,
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }
}
