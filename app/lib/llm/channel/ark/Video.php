<?php

namespace app\lib\llm\channel\ark;

use app\lib\llm\contract\VideoInterface;
use think\helper\Arr;

class Video extends Driver implements VideoInterface
{
    public function getFeatures($model)
    {
        return match ($model) {
            'doubao-seedance-1-0-pro-250528' => [
                'text'        => true,
                'image'       => true,
                'resolutions' => ['480P', '1080P'],
                'ratios'      => ['16:9', '4:3', '1:1', '3:4', '9:16', '21:9'],
                'durations'   => [5, 10],
            ],
            'doubao-seedance-1-0-lite-i2v-250428' => [
                'text'        => true,
                'resolutions' => ['480P', '720P', '1080P'],
                'ratios'      => ['16:9', '4:3', '1:1', '3:4', '9:16', '21:9'],
                'durations'   => [5, 10],
            ],
            'doubao-seedance-1-0-lite-t2v-250428' => [
                'image'       => true,
                'resolutions' => ['480P', '720P', '1080P'],
                'ratios'      => ['16:9', '4:3', '1:1', '3:4', '9:16', '21:9'],
                'durations'   => [5, 10],
            ],
            default => []
        };
    }

    public function generations($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $prompt     = $params['prompt'] ?? '';
        $ratio      = $params['ratio'] ?? '16:9';
        $duration   = $params['duration'] ?? 5;
        $resolution = strtolower($params['resolution'] ?? '480P');

        $prompt .= "  --resolution {$resolution}  --ratio {$ratio}  --duration {$duration}";

        $content = [
            [
                'type' => 'text',
                'text' => $prompt,
            ],
        ];

        if (!empty($params['image'])) {
            $content[] = [
                'type'      => 'image_url',
                'image_url' => [
                    'url' => $this->getImageUrl($params['image']),
                ],
            ];
        }

        $result = $this->request('contents/generations/tasks', [
            'json' => [
                'model'   => $model,
                'content' => $content,
            ],
        ]);

        $factor = match ($resolution) {
            '1080p' => 5.1,
            '720p' => 2.5,
            '480p' => 1,
            default => 1
        };

        $usage = 1 * $factor * $duration;

        return [
            'id'    => Arr::get($result, 'id'),
            'usage' => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function query($params)
    {
        $result = $this->request("contents/generations/tasks/{$params['id']}", [], 'GET');

        $status = match ($result['status']) {
            'queued', 'running' => 'processing',
            'succeeded' => 'success',
            'cancelled', 'failed' => 'fail',
        };

        if ($status == 'success') {
            $videos = [
                [
                    'url' => Arr::get($result, 'content.video_url'),
                ],
            ];
        }

        return [
            'status' => $status,
            'videos' => $videos ?? null,
            'error'  => null,
        ];
    }
}
