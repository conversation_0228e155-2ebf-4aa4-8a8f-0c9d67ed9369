<?php

namespace app\lib\llm\channel\topthink;

use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use think\helper\Str;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = '顶想云';

    public function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;

        if (!$isOk) {
            $content = $response->getBody()->getContents();
            $result  = json_decode($content, true);
            if ($result) {
                throw new Exception($result['message'] ?? 'Unknown error');
            }
            throw new Exception('Unknown error');
        }

        $contentType = $response->getHeaderLine('Content-Type');

        if (Str::startsWith($contentType, 'text/event-stream')) {
            return $response->getBody();
        }

        if (!Str::contains($contentType, 'json')) {
            return $response;
        }

        return json_decode($response->getBody()->getContents(), true);
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://ai.topthink.com'),
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth()}",
            ],
        ];
    }
}
