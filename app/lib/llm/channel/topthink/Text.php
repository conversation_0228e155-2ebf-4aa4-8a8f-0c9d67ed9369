<?php

namespace app\lib\llm\channel\topthink;

use app\lib\llm\contract\TextInterface;
use app\lib\llm\traits\DefaultText;
use think\helper\Arr;

class Text extends Driver implements TextInterface
{
    use DefaultText;

    public function embedding($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $input = $params['input'] ?? '';

        $multi = true;
        if (!is_array($input)) {
            $input = [$input];
            $multi = false;
        }

        $usage  = 0;
        $chunks = array_chunk($input, 10);

        $embeddings = array_reduce($chunks, function ($carry, $item) use ($model, &$usage, $multi) {
            $result = $this->request('/embeddings', [
                'json' => [
                    'model' => $model,
                    'input' => $item,
                ],
            ]);

            $usage += Arr::get($result, 'usage.total_tokens', 0);

            return array_merge($carry, Arr::get($result, 'embeddings', []));
        }, []);

        if (!$multi) {
            $embeddings = Arr::get($embeddings, '0', []);
        }

        return [
            'embeddings' => $embeddings,
            'usage'      => $this->applyFactor([
                'prompt_tokens'     => 0,
                'completion_tokens' => $usage,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function rerank($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $result = $this->request('/rerank', [
            'json' => [
                'model'     => $model,
                'query'     => $params['query'],
                'documents' => $params['documents'],
            ],
        ]);

        $usage = Arr::get($result, 'usage.total_tokens', 0);

        return [
            'documents' => $result['documents'],
            'usage'     => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }
}
