<?php

namespace app\lib\llm\channel\topthink;

use app\lib\llm\contract\AudioInterface;
use app\lib\llm\traits\DefaultAudio;

class Audio extends Driver implements AudioInterface
{
    use DefaultAudio;

    public function speech($params)
    {
        $model = $this->options['checkpoint'] ?? null;
        $input = $params['input'] ?? '';
        $voice = $params['voice'] ?? '';
        $speed = $params['speed'] ?? 1;

        $result = $this->request('/audio/speech', [
            'json' => [
                'model' => $model,
                'input' => $input,
                'voice' => $voice,
                'speed' => $speed,
            ],
        ]);

        return [
            'audio' => $result['audio'],
            'usage' => $this->applyFactor($result['usage']),
        ];
    }

    public function transcriptions($params)
    {
        $result = $this->request('/audio/transcriptions', [
            'json' => [
                'model'    => $this->options['checkpoint'] ?? null,
                'url'      => $params['url'] ?? null,
                'language' => $params['language'] ?? null,
            ],
        ]);

        return [
            'text'  => $result['text'],
            'usage' => $this->applyFactor($result['usage']),
        ];
    }
}
