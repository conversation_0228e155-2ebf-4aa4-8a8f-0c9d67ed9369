<?php

namespace app\lib\llm\channel\anthropic;

use app\lib\llm\channel\OpenAiCompatibleResponse;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = 'Anthropic';

    use OpenAiCompatibleResponse;

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://api.anthropic.com'),
            'headers'  => [
                'anthropic-version' => '2023-06-01',
                'x-api-key'         => $this->getAuth(),
            ],
        ];
    }
}
