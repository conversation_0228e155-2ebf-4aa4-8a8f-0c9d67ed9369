<?php

namespace app\lib\llm\channel\minimax;

use app\lib\llm\contract\VideoInterface;
use think\helper\Arr;

class Video extends Driver implements VideoInterface
{

    public function getFeatures($model)
    {
        return match ($model) {
            'MiniMax-Hailuo-02' => [
                'image'       => true,
                'text'        => true,
                'resolutions' => ['768P', '1080P'],
                'durations'   => [6, 10],
            ],
            default => [],
        };
    }

    protected function transformData(&$data, $model, $params)
    {
        switch ($model) {
            case 'MiniMax-Hailuo-02':
                $data['resolution'] = $params['resolution'] ?? '768P';
                $data['duration']   = $params['duration'] ?? 6;
                break;
        }
    }

    public function generations($params)
    {
        $model      = $this->options['checkpoint'] ?? null;
        $prompt     = $params['prompt'] ?? '';
        $image      = $params['image'] ?? null;
        $duration   = $params['duration'] ?? 6;
        $resolution = $params['resolution'] ?? '768P';

        $data = [
            'model'             => $model,
            'prompt'            => $prompt,
            'first_frame_image' => $this->getImageUrl($image),
        ];

        $this->transformData($data, $model, $params);

        $result = $this->request('video_generation', [
            'json' => $data,
        ]);

        $factor = match ($resolution) {
            '1080P' => 1.75,
            '768P' => 1,
        };

        $usage = $duration * $factor;

        return [
            'id'    => Arr::get($result, 'task_id'),
            'usage' => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

    public function query($params)
    {
        $result = $this->request("query/video_generation", [
            'query' => [
                'task_id' => $params['id'],
            ],
        ], 'GET');

        $status = match ($result['status']) {
            'Queueing', 'Processing' => 'processing',
            'Success' => 'success',
            'Failed' => 'fail',
        };

        if ($status == 'success') {
            $result = $this->request('files/retrieve', [
                'query' => [
                    'file_id' => $result['file_id'],
                ],
            ], 'GET');

            $videos = [
                [
                    'url' => Arr::get($result, 'file.download_url'),
                ],
            ];
        }

        return [
            'status' => $status,
            'videos' => $videos ?? null,
        ];
    }
}
