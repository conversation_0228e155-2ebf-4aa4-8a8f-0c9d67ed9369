<?php

namespace app\lib\llm\channel\minimax;

use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use think\helper\Arr;
use think\helper\Str;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = 'Minimax';

    public function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;

        if (!$isOk) {
            throw new Exception('Request error');
        }

        $contentType = $response->getHeaderLine('Content-Type');

        if (Str::startsWith($contentType, 'text/event-stream')) {
            return $response->getBody();
        }

        $result = json_decode($response->getBody()->getContents(), true);

        $code = Arr::get($result, 'base_resp.status_code', -1);
        if ($code != 0) {
            throw new Exception(Arr::get($result, 'base_resp.status_msg', 'Unknown error'));
        }

        return $result;
    }

    protected function getClientOptions()
    {
        return [
            'base_uri' => $this->getBaseUri('https://api.minimax.chat/v1/'),
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth()}",
            ],
        ];
    }
}
