<?php

namespace app\lib\llm\channel\minimax;

use app\lib\llm\contract\MusicInterface;
use app\lib\llm\traits\DefaultMusic;
use think\helper\Arr;

class Music extends Driver implements MusicInterface
{
    use DefaultMusic;

    public function getFeatures($model)
    {
        return [
            'song' => match ($model) {
                default => [
                    'durations' => [60],
                ]
            },
        ];
    }

    public function song($params)
    {
        $model  = $this->options['checkpoint'] ?? null;
        $prompt = $params['prompt'] ?? '';

        $data = [
            'model'         => $model,
            'prompt'        => $prompt,
            'lyrics'        => $params['lyrics'] ?? null,
            'output_format' => 'url',
        ];

        $result = $this->request('music_generation', [
            'json' => $data,
        ]);

        $usage = 60;

        $audios = [
            [
                'url' => Arr::get($result, 'data.audio'),
            ],
        ];

        return [
            'audios' => $audios,
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }

}
