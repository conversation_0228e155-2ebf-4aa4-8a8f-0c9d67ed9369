<?php

namespace app\lib\llm\channel\minimax;

use app\lib\llm\contract\TextInterface;
use app\lib\llm\traits\DefaultText;
use think\helper\Arr;

class Text extends Driver implements TextInterface
{
    use DefaultText;

    public function embedding($params)
    {
        $model = $this->options['checkpoint'] ?? null;

        $input = $params['input'] ?? '';

        $multi = true;
        if (!is_array($input)) {
            $input = [$input];
            $multi = false;
        }

        $usage  = 0;
        $chunks = array_chunk($input, 10);

        $embeddings = array_reduce($chunks, function ($carry, $item) use ($model, &$usage, $multi) {
            $result = $this->request('embeddings', [
                'query' => [
                    'GroupId' => '1782363907101311037',
                ],
                'json'  => [
                    'model' => $model,
                    'texts' => $item,
                    'type'  => $multi ? 'db' : 'query',
                ],
            ]);

            $usage += Arr::get($result, 'total_tokens', 0);

            return array_merge($carry, Arr::get($result, 'vectors', []));
        }, []);

        if (!$multi) {
            $embeddings = Arr::get($embeddings, '0', []);
        }

        return [
            'embeddings' => $embeddings,
            'usage'      => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }
}
