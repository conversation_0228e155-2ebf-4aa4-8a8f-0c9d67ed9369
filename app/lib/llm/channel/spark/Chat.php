<?php

namespace app\lib\llm\channel\spark;

use app\lib\llm\contract\ChatInterface;
use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use Psr\Http\Message\StreamInterface;
use think\helper\Str;

class Chat extends Driver implements ChatInterface
{
    public function completions($params)
    {
        $model = $this->options['checkpoint'] ?? '';

        $messages = $params['messages'] ?? [];
        $tools    = $params['tools'] ?? null;
        $stream   = $params['stream'] ?? true;

        $baseUri = $this->getBaseUri('https://spark-api-open.xf-yun.com/v1/');

        $res = $this->request('chat/completions', [
            'base_uri' => $baseUri,
            'json'     => [
                'model'             => $model,
                'messages'          => $messages,
                'stream'            => $stream,
                'tools'             => $tools,
                'temperature'       => $params['temperature'] ?? 1.0,
                'max_tokens'        => $params['max_tokens'] ?? 4096,
                'response_format'   => $params['response_format'] ?? null,
                'tool_calls_switch' => true,
            ],
            'headers'  => [
                'Authorization' => "Bearer {$this->getAuth('api_password')}",
            ],
            'stream'   => $stream,
        ]);

        if ($res instanceof StreamInterface) {
            $call = null;

            foreach ($this->getMessages($res) as $message) {
                $data = $message['data'];

                if ($data == '[DONE]') {
                    break;
                }

                $result = json_decode($data, true);

                $delta = $result['choices'][0]['delta'] ?? null;
                $usage = $result['usage'] ?? null;
                //检查是否为tools_call
                if (!empty($delta['tool_calls'])) {
                    $current = $delta['tool_calls'][0];
                    if ($call) {
                        if ($call['index'] != $current['index']) {
                            yield [
                                'delta'         => [
                                    'role'       => 'assistant',
                                    'content'    => null,
                                    'tool_calls' => [$call],
                                ],
                                'finish_reason' => null,
                            ];
                            $call = $current;
                        } else {
                            $call['function']['arguments'] .= $current['function']['arguments'];
                        }
                    } else {
                        $call = $current;
                    }

                    if (!empty($delta)) {
                        yield [
                            'delta'         => [
                                'role'       => 'assistant',
                                'content'    => null,
                                'tool_calls' => [$call],
                            ],
                            'finish_reason' => null,
                        ];
                        $call = null;
                    }
                } else {
                    if (!empty($delta)) {
                        if (!empty($delta['reasoning_content'])) {
                            $delta['reasoning'] = $delta['reasoning_content'];
                            unset($delta['reasoning_content']);
                        }
                        yield [
                            'delta'         => $delta,
                            'finish_reason' => null,
                        ];
                    }
                }
                if (!empty($usage)) {
                    yield [
                        'usage'         => $this->applyFactor($usage),
                        'finish_reason' => 'stop',
                    ];
                }
            }
        } else {
            $message = $res['choices'][0]['message'];
            $usage   = $res['usage'];

            if (!empty($message['reasoning_content'])) {
                $message['reasoning'] = $message['reasoning_content'];
                unset($message['reasoning_content']);
            }

            return [
                'message'       => $message,
                'finish_reason' => 'stop',
                'usage'         => $this->applyFactor($usage),
            ];
        }
    }

    protected function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;

        if (!$isOk) {
            $content = $response->getBody()->getContents();
            $result  = json_decode($content, true);
            if ($result) {
                throw new Exception($result['error']['message'] ?? $result['message'] ?? 'Unknown error');
            }
            throw new Exception('Unknown error');
        }

        $contentType = $response->getHeaderLine('Content-Type');

        if (Str::startsWith($contentType, 'text/event-stream')) {
            return $response->getBody();
        }

        return json_decode($response->getBody()->getContents(), true);
    }
}
