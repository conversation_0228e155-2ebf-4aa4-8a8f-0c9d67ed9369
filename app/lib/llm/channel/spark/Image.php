<?php

namespace app\lib\llm\channel\spark;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\Exception;
use app\lib\llm\traits\DefaultImage;
use think\helper\Arr;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function getFeatures($model)
    {
        return [
            'size' => ['1024x1024', '720x1280', '1280x720'],
        ];
    }

    public function generations($params)
    {
        $prompt = $params['prompt'] ?? '';
        $size   = $params['size'] ?? '1024x1024';
        $format = $params['response_format'] ?? 'b64_json';

        if ($format != 'b64_json') {
            throw new Exception("not support format: {$format}");
        }

        $sizes = [
            '1024x1024' => 14,
            '720x1280'  => 12,
            '1280x720'  => 12,
        ];

        if (!array_key_exists($size, $sizes)) {
            throw new Exception("not support size: {$size}");
        }

        [$width, $height] = explode('x', $size);

        $uri = 'https://spark-api.cn-huabei-1.xf-yun.com/v2.1/tti';

        $res = $this->request($uri, [
            'query' => $this->assembleAuthUrl('POST', $uri),
            'json'  => [
                'header'    => [
                    'app_id' => $this->getAuth('app_id'),
                ],
                'parameter' => [
                    'chat' => [
                        'domain' => 'general',
                        'width'  => (int) $width,
                        'height' => (int) $height,
                    ],
                ],
                'payload'   => [
                    'message' => [
                        'text' => [
                            [
                                'role'    => 'user',
                                'content' => $prompt,
                            ],
                        ],
                    ],
                ],
            ],
        ]);

        $usage = round($sizes[$size] / 14, 2);

        return [
            'images' => [
                [
                    'b64_json' => Arr::get($res, 'choices.text.0.content'),
                ],
            ],
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => $usage,
                'completion_tokens' => 0,
                'total_tokens'      => $usage,
            ]),
        ];
    }
}
