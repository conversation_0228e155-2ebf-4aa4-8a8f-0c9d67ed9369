<?php

namespace app\lib\llm\channel\spark;

use app\lib\llm\Exception;
use Psr\Http\Message\ResponseInterface;
use think\helper\Arr;

abstract class Driver extends \app\lib\llm\channel\Driver
{
    public const NAME = '星火';

    protected function assembleAuthUrl($method, $url)
    {
        $parsed = parse_url($url);

        $date = gmdate('D, d M Y H:i:s T');

        $str = "host: {$parsed['host']}\ndate: {$date}\n{$method} {$parsed['path']} HTTP/1.1";

        $signature     = base64_encode(hash_hmac('sha256', $str, $this->getAuth('api_secret'), true));
        $authorization = base64_encode("api_key=\"{$this->getAuth('api_key')}\", algorithm=\"hmac-sha256\", headers=\"host date request-line\", signature=\"{$signature}\"");

        return [
            'host'          => $parsed['host'],
            'date'          => $date,
            'authorization' => $authorization,
        ];
    }

    protected function transformResponse(ResponseInterface $response)
    {
        $statusCode = $response->getStatusCode();
        $isOk       = $statusCode == 200;

        $result = json_decode($response->getBody()->getContents(), true);

        if (!$isOk) {
            throw new Exception(Arr::get($result, 'message', 'Unknown error'));
        }

        if (Arr::get($result, 'header.code') !== 0) {
            throw new Exception(Arr::get($result, 'header.message', 'Unknown error'));
        }

        return Arr::get($result, 'payload');
    }

    protected function getClientOptions()
    {
        return [];
    }
}
