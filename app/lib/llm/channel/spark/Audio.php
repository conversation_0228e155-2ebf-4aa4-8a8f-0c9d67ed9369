<?php

namespace app\lib\llm\channel\spark;

use app\lib\llm\contract\AudioInterface;
use app\lib\llm\Exception;
use app\lib\llm\traits\DefaultAudio;
use Swoole\Coroutine\Http\Client;
use Swoole\WebSocket\CloseFrame;
use Swoole\WebSocket\Frame;
use think\helper\Arr;

class Audio extends Driver implements AudioInterface
{
    use DefaultAudio;

    public function speech($params)
    {
        $input = $params['input'] ?? '';
        $voice = $params['voice'] ?? '';
        $speed = $params['speed'] ?? 1;

        $uri = 'wss://cbm01.cn-huabei-1.xf-yun.com/v1/private/mcd9m97e6';

        $parsed = parse_url($uri);
        $client = new Client($parsed['host'], 443, true);

        $query = $this->assembleAuthUrl('GET', $uri);

        $res = $client->upgrade($parsed['path'] . '?' . http_build_query($query));

        if (!$res) {
            throw new Exception('connect failed');
        }

        $client->push(json_encode([
            'header'    => [
                'app_id' => $this->getAuth('app_id'),
                'status' => 2,
            ],
            'parameter' => [
                'oral' => [
                    'oral_level' => 'mid',
                ],
                'tts'  => [
                    'vcn'   => $voice,
                    'speed' => 50,
                    'audio' => [
                        'encoding' => 'lame',
                    ],
                ],
            ],
            'payload'   => [
                'text' => [
                    'encoding' => 'utf8',
                    'compress' => 'raw',
                    'format'   => 'plain',
                    'status'   => 2,
                    'seq'      => 0,
                    'text'     => base64_encode($input),
                ],
            ],
        ]));

        while (true) {
            $res = $client->recv();
            if ($res) {
                if ($res instanceof CloseFrame || !$res) {
                    break;
                }
                if ($res instanceof Frame) {
                    $result = json_decode($res->data, true);

                    if (Arr::get($result, 'header.code') !== 0) {
                        throw new Exception(Arr::get($result, 'header.message', 'Unknown error'));
                    }

                    $payload = Arr::get($result, 'payload');
                    if ($payload) {
                        $text = json_decode(base64_decode(Arr::get($payload, 'result.text')), true);

                        return [
                            'text'  => array_reduce($text['ws'], function ($prev, $ws) {
                                return array_reduce($ws['cw'], function ($prev, $cw) {
                                    return $prev . $cw['w'];
                                }, $prev);
                            }, ''),
                            'usage' => $this->applyFactor([
                                'prompt_tokens'     => 0,
                                'completion_tokens' => 1,
                                'total_tokens'      => 1,
                            ]),
                        ];
                    }
                }
            }
        }
    }

    public function transcriptions($params)
    {
        /** @var \think\file\UploadedFile $file */
        $file = $params['file'];
        $mime = $file->getOriginalMime();

        $uri = 'wss://iat.cn-huabei-1.xf-yun.com/v1';

        $parsed = parse_url($uri);
        $client = new Client($parsed['host'], 443, true);

        $query = $this->assembleAuthUrl('GET', $uri);

        $res = $client->upgrade($parsed['path'] . '?' . http_build_query($query));

        if (!$res) {
            throw new Exception('connect failed');
        }

        $encoding = $mime == 'audio/mpeg' ? 'lame' : 'raw';

        $client->push(json_encode([
            'header'    => [
                'app_id' => $this->getAuth('app_id'),
                'status' => 0,
            ],
            'parameter' => [
                "iat" => [
                    "domain"   => 'slm',
                    'language' => 'mul_cn',
                    'accent'   => 'mandarin',
                    'result'   => [
                        'encoding' => 'utf8',
                        'compress' => 'raw',
                        'format'   => 'json',
                    ],
                ],
            ],
            'payload'   => [
                'audio' => [
                    'audio'    => base64_encode(file_get_contents($file->getRealPath())),
                    'encoding' => $encoding,
                ],
            ],
        ]));

        $client->push(json_encode([
            'header'  => [
                'app_id' => $this->getAuth('app_id'),
                'status' => 2,
            ],
            'payload' => [
                'audio' => [
                    'audio'    => '',
                    'encoding' => $encoding,
                ],
            ],
        ]));

        while (true) {
            $res = $client->recv();
            if ($res) {
                if ($res instanceof CloseFrame || !$res) {
                    break;
                }
                if ($res instanceof Frame) {
                    $result = json_decode($res->data, true);

                    if (Arr::get($result, 'header.code') !== 0) {
                        throw new Exception(Arr::get($result, 'header.message', 'Unknown error'));
                    }

                    $payload = Arr::get($result, 'payload');
                    if ($payload) {
                        $text = json_decode(base64_decode(Arr::get($payload, 'result.text')), true);

                        return [
                            'text'  => array_reduce($text['ws'], function ($prev, $ws) {
                                return array_reduce($ws['cw'], function ($prev, $cw) {
                                    return $prev . $cw['w'];
                                }, $prev);
                            }, ''),
                            'usage' => $this->applyFactor([
                                'prompt_tokens'     => 0,
                                'completion_tokens' => 1,
                                'total_tokens'      => 1,
                            ]),
                        ];
                    }
                }
            }
        }
    }
}
