<?php

namespace app\lib\llm\channel\flux;

use app\lib\llm\contract\ImageInterface;
use app\lib\llm\Exception;
use app\lib\llm\traits\DefaultImage;
use think\helper\Arr;

class Image extends Driver implements ImageInterface
{
    use DefaultImage;

    public function getFeatures($model)
    {
        return match ($model) {
            'flux-kontext-pro', 'flux-kontext-max' => [
                'ratios' => ['1:1', '4:3', '3:4', '16:9', '9:16', '3:2', '2:3', '5:4', '4:5', '21:9', '9:21'],
                'seed'   => true,
            ],
            default => throw new Exception('not support model: ' . $model)
        };
    }

    public function generations($params)
    {
        $model       = $this->options['checkpoint'];
        $prompt      = $params['prompt'] ?? '';
        $aspectRatio = $params['ratio'] ?? '1:1';

        $format = $params['response_format'] ?? 'url';

        if ($format !== 'url') {
            throw new Exception("not support format: {$format}");
        }

        // 构建请求数据，根据模型支持的参数进行过滤
        $data = array_filter_null([
            'prompt'       => $prompt,
            'aspect_ratio' => $aspectRatio,
            'seed'         => $params['seed'] ?? null,
        ]);

        // 提交生成请求
        $result = $this->request("/v1/{$model}", [
            'json' => $data,
        ]);

        $taskId     = Arr::get($result, 'id');
        $pollingUrl = Arr::get($result, 'polling_url');

        if (empty($taskId)) {
            throw new Exception('Failed to get task ID from response');
        }

        // 轮询结果
        $finalResult = $this->pollForResult($pollingUrl ?: "/v1/get_result?id={$taskId}");

        return [
            'images' => [
                ['url' => Arr::get($finalResult, 'result.sample')],
            ],
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => 1,
                'completion_tokens' => 0,
                'total_tokens'      => 1,
            ]),
        ];
    }

    public function edit($params)
    {
        $model  = $this->options['checkpoint'];
        $prompt = $params['prompt'] ?? '';
        $image  = $params['image'] ?? null;
        $format = $params['response_format'] ?? 'url';

        if ($format !== 'url') {
            throw new Exception("not support format: {$format}");
        }

        if (empty($image)) {
            throw new Exception('Image is required for editing');
        }

        // 处理图像数据
        $imageData = $this->getImageData($image);

        $data = array_filter_null([
            'prompt'      => $prompt,
            'input_image' => $imageData,
        ]);

        // 提交编辑请求
        $result = $this->request("/v1/{$model}", [
            'json' => $data,
        ]);

        $taskId     = Arr::get($result, 'id');
        $pollingUrl = Arr::get($result, 'polling_url');

        if (empty($taskId)) {
            throw new Exception('Failed to get task ID from response');
        }

        // 轮询结果
        $finalResult = $this->pollForResult($pollingUrl ?: "/v1/get_result?id={$taskId}");

        return [
            'images' => [
                ['url' => Arr::get($finalResult, 'result.sample')],
            ],
            'usage'  => $this->applyFactor([
                'prompt_tokens'     => 1,
                'completion_tokens' => 0,
                'total_tokens'      => 1,
            ]),
        ];
    }

    protected function pollForResult($pollingUrl, $maxWaitTime = 300)
    {
        $startTime = time();

        while (true) {
            $result = $this->request($pollingUrl, [], 'GET');
            $status = Arr::get($result, 'status');

            if ($status === 'Ready') {
                return $result;
            }

            if (in_array($status, ['Error', 'Failed'])) {
                $errorMessage = Arr::get($result, 'error.message',
                    Arr::get($result, 'message', 'Generation failed'));
                throw new Exception($errorMessage);
            }

            if (time() - $startTime > $maxWaitTime) {
                throw new Exception('Generation timeout');
            }

            // 等待0.5秒后重试
            usleep(500000);
        }
    }
}
