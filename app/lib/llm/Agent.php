<?php

namespace app\lib\llm;

use app\lib\Llm;
use app\lib\llm\tool\result\Raw;
use app\lib\Util;
use app\model\Model;
use app\model\Response;
use think\exception\ValidateException;
use think\helper\Arr;
use Throwable;

class Agent
{
    protected $usage  = 0;
    protected $round  = 0;
    protected $chunks = [];

    protected $canUseTool = true;
    protected $maxTokens  = 0;

    protected Model    $model;
    protected Response $response;

    public function __construct(protected Llm $llm, $data)
    {
        $this->model = Model::where('code', $data['model'])->where('type', 'chat')->find();

        if (!$this->model) {
            throw new ValidateException("The model `{$data['model']}` does not exist");
        }

        $this->canUseTool = $this->model->params['tool'] ?? false;
        $this->maxTokens  = $this->model->params['context_tokens'] ?? 0;

        $this->response = new Response([
            'previous_id'  => $data['previous_id'] ?? null,
            'model'        => $this->model->code,
            'input'        => $data['input'],
            'tools'        => $data['tools'] ?? [],
            'instructions' => $data['instructions'] ?? null,
        ]);
    }

    protected function start()
    {
        $messages = $this->buildPromptMessages();
        $tools    = $this->buildTools();
        yield from $this->iteration($messages, $tools);
    }

    public function run()
    {
        try {
            $start = microtime(true);
            yield from $this->start();
        } catch (Throwable $e) {
            yield from $this->sendChunkData($this->round, 'error', $e->getMessage());
        } finally {
            $latency = round((microtime(true) - $start) * 1000);

            //更新统计
            yield [
                'stats' => [
                    'usage'   => $this->usage,
                    'latency' => $latency,
                ],
            ];

            if (!empty($this->chunks)) {
                $this->response->save([
                    'output' => $this->chunks,
                ]);

                //更新消息ID
                yield [
                    'id' => $this->response->hash_id,
                ];
            }

            $this->round  = 0;
            $this->usage  = 0;
            $this->chunks = [];
        }
    }

    protected function iteration($messages, $tools)
    {
        $chunkIndex = $this->round;

        $params = [
            'model'    => $this->model,
            'messages' => $messages,
        ];

        if (!empty($tools)) {
            $params['tools'] = $tools;
        }

        $calls = [];

        try {
            $result = $this->llm->chat()->completions($params);
            $this->round++;

            foreach ($result as $event) {
                if (!empty($event['delta']['tool_calls'])) {
                    $call      = $event['delta']['tool_calls'][0];
                    $callIndex = $call['index'] ?? 0;
                    unset($call['index']);

                    if (!isset($calls[$callIndex])) {
                        $calls[$callIndex] = $call;

                        //下发调用工具的状态
                        switch ($call['type']) {
                            case 'plugin':
                                yield from $this->sendToolData($chunkIndex, $callIndex, [
                                    'id'        => $call['id'],
                                    'name'      => $call['plugin']['function'],
                                    'title'     => $call['plugin']['title'],
                                    'arguments' => $call['plugin']['arguments'],
                                ]);
                                break;
                        }
                    } else {
                        $calls[$callIndex] = Arr::mergeDeep($calls[$callIndex], $call);
                    }
                } else {
                    $reasoning = $event['delta']['reasoning'] ?? '';
                    if ($reasoning !== '') {//这里必须和''强比较，防止0等字符不能输出
                        yield from $this->sendChunkData($chunkIndex, 'reasoning', $reasoning, true);
                    }
                    $content = $event['delta']['content'] ?? '';
                    if ($content !== '') {//这里必须和''强比较，防止0等字符不能输出
                        yield from $this->sendChunkData($chunkIndex, 'content', $content, true);
                    }
                }

                if (!empty($event['usage'])) {
                    $this->usage += $event['usage']['total_tokens'];
                    yield from $this->sendChunkData($chunkIndex, 'content', '', true);
                }
            }
        } catch (Throwable $e) {
            yield from $this->sendChunkData($chunkIndex, 'error', $e->getMessage());
        }

        if (!empty($calls)) {
            $messages[] = [
                'role'       => 'assistant',
                'tool_calls' => $calls,
            ];

            foreach ($calls as $index => $call) {
                $id   = $call['id'];
                $type = $call['type'];

                switch ($type) {
                    case 'plugin':
                        $result = new Raw([
                            'response' => $call['plugin']['response'],
                            'content'  => $call['plugin']['content'],
                            'error'    => $call['plugin']['error'],
                            'usage'    => $call['plugin']['usage'],
                        ]);
                        break;
                }

                if (!empty($result)) {
                    //调用工具产生的计费
                    $this->usage += $result->getUsage();

                    $content = $result->getContent();

                    //下发调用工具完成的状态
                    yield from $this->sendToolData($chunkIndex, $index, [
                        'response' => $result->getResponse(),
                        'error'    => $result->isError(),
                        'content'  => $content,
                    ]);
                }
            }

            yield from $this->iteration($messages, $tools);
        }
    }

    protected function buildTools()
    {
        if (!$this->canUseTool) {
            return null;
        }

        $tools = [];

        foreach ($this->response->tools ?? [] as $tool) {
            switch ($tool['type']) {
                case 'plugin':
                    $tools[] = $tool;
                    break;
            }
        }

        return $tools;
    }

    protected function buildPromptMessages()
    {
        $promptMessages = [];

        if (!empty($this->response->instructions)) {
            $promptMessages[] = [
                'role'    => 'system',
                'content' => $this->response->instructions,
            ];
        }

        $historyMessages = $this->getHistoryMessages();
        $promptMessages  = array_merge($promptMessages, $historyMessages);

        $promptMessages[] = [
            'role'    => 'user',
            'content' => $this->response->input,
        ];

        return $promptMessages;
    }

    protected function getHistoryMessages()
    {
        $historyMessages = [];

        $previousId = $this->response->previous_id ?? null;

        while ($previousId) {
            $response = Response::fromHashId($previousId);
            if ($response) {
                $chunkMessages = [
                    [
                        'role'    => 'user',
                        'content' => $response->input,
                    ],
                ];

                foreach ($response->output as $chunk) {
                    if (!empty($chunk['error'])) {
                        break 2;
                    }
                    if (!empty($chunk['tools'])) {
                        if (!$this->canUseTool) {
                            break 2;
                        }
                        $calls     = [];
                        $responses = [];
                        foreach ($chunk['tools'] as $tool) {
                            $calls[] = [
                                'id'       => $tool['id'],
                                'type'     => 'function',
                                'function' => [
                                    'name'      => $tool['name'],
                                    'arguments' => $tool['arguments'],
                                ],
                            ];

                            $responses[] = [
                                'tool_call_id' => $tool['id'],
                                'role'         => 'tool',
                                'name'         => $tool['name'],
                                'content'      => $tool['response'],
                            ];
                        }

                        $chunkMessages[] = [
                            'role'       => 'assistant',
                            'content'    => $chunk['content'] ?? null,
                            'tool_calls' => $calls,
                        ];

                        $chunkMessages = array_merge($chunkMessages, $responses);
                    } else {
                        $chunkMessages[] = [
                            'role'    => 'assistant',
                            'content' => $chunk['content'] ?? '',
                        ];
                    }
                }

                $tempHistoryMessages = array_merge($chunkMessages, $historyMessages);
                if ($this->maxTokens > 0) {
                    $tokens = Util::tikToken($tempHistoryMessages);
                    if ($tokens > $this->maxTokens * .6) {
                        break;
                    }
                }
                $historyMessages = $tempHistoryMessages;

                $previousId = $response->previous_id;
            } else {
                $previousId = null;
            }
        }

        return $historyMessages;
    }

    protected function sendToolData($chunkIndex, $toolIndex, $data)
    {
        $this->updateChunk($chunkIndex, "tools.{$toolIndex}", $data);

        yield [
            'chunks' => [
                'index' => $chunkIndex,
                'tools' => [
                    'index' => $toolIndex,
                    ...$data,
                ],
            ],
        ];
    }

    protected function sendChunkData($chunkIndex, $key, $value, $append = false)
    {
        $this->updateChunk($chunkIndex, $key, $value, $append);

        yield [
            'chunks' => [
                'index' => $chunkIndex,
                $key    => $value,
            ],
        ];
    }

    protected function updateChunk($chunkIndex, $key, $value, $append = false)
    {
        if (is_array($value)) {
            foreach ($value as $k => $v) {
                Arr::set($this->chunks, "{$chunkIndex}.{$key}.{$k}", $v);
            }
        } else {
            if ($append) {
                $value = Arr::get($this->chunks, "{$chunkIndex}.{$key}", '') . $value;
            }
            Arr::set($this->chunks, "{$chunkIndex}.{$key}", $value);
        }
    }
}
