<?php

namespace app\lib\llm\mcp;

class StreamableHttp implements Transport
{

    public function listTools()
    {
        // TODO: Implement listTools() method.
    }

    public function callTool(string $name, ?array $arguments = null)
    {
        // TODO: Implement callTool() method.
    }

    public static function connect($url): Transport
    {
        // TODO: Implement connect() method.
    }
}
