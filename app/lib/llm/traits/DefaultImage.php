<?php

namespace app\lib\llm\traits;

use app\lib\llm\Exception;

trait DefaultImage
{
    public function generations($params)
    {
        throw new Exception('not support');
    }

    public function inpainting($params)
    {
        throw new Exception('not support');
    }

    public function outpainting($params)
    {
        throw new Exception('not support');
    }

    public function upscale($params)
    {
        throw new Exception('not support');
    }

    public function poster($params)
    {
        throw new Exception('not support');
    }

    public function edit($params)
    {
        throw new Exception('not support');
    }
}
