<?php

namespace app\lib\llm\tool;

abstract class Result implements \JsonSerializable
{
    protected $usage = 0;

    protected $error = false;

    public function isError()
    {
        return $this->error;
    }

    public function setUsage($usage)
    {
        $this->usage = $usage;
        return $this;
    }

    public function getUsage()
    {
        return $this->usage;
    }

    public function getContent()
    {
        return '';
    }

    abstract public function getResponse();

    public function jsonSerialize(): mixed
    {
        return [
            'usage'    => $this->getUsage(),
            'content'  => $this->getContent(),
            'response' => $this->getResponse(),
        ];
    }
}
