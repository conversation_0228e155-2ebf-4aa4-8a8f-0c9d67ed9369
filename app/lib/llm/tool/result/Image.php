<?php

namespace app\lib\llm\tool\result;

use app\lib\llm\tool\Result;

class Image extends Result
{
    public function __construct(protected $image)
    {
    }

    public function getContent()
    {
        return [
            'type'  => 'image',
            'image' => $this->image,
        ];
    }

    public function getResponse()
    {
        return "image has been created and sent to user already, you do not need to create it, just tell the user to check it now.";
    }
}
