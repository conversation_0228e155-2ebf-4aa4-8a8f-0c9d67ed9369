<?php

namespace app\lib\llm\plugin;

use app\lib\Llm;
use app\lib\llm\contract\ToolkitInterface;
use app\lib\llm\Tool;
use app\lib\llm\tool\Args;
use app\lib\llm\tool\result\Plain;
use think\helper\Arr;

class Vision implements ToolkitInterface
{
    protected $models;

    public function __construct($config)
    {
        $this->models = Arr::get($config, 'models', []);
    }

    public function getTools()
    {
        return array_map(function ($tool) {
            return new class($tool) extends Tool {
                protected $description = '回答用户关于图像的问题';

                protected $parameters = [
                    'query' => [
                        'type'        => 'string',
                        'description' => '用户关于图片的问题',
                        'required'    => true,
                    ],
                    'url'   => [
                        'type'        => 'string',
                        'description' => '图片URL地址',
                        'required'    => true,
                    ],
                ];

                public function __construct(protected $config)
                {
                    $this->title = $this->config['title'];
                    $this->name  = $this->config['name'];
                }

                public function run(Args $args)
                {
                    $res = app()->make(Llm::class, newInstance: true)
                        ->chat()
                        ->completions([
                            'model'      => $this->config['code'],
                            'messages'   => [
                                [
                                    'role'    => 'user',
                                    'content' => [
                                        [
                                            'type' => 'text',
                                            'text' => $args['query'],
                                        ],
                                        [
                                            'type'      => 'image_url',
                                            'image_url' => [
                                                'url' => $args['url'],
                                            ],
                                        ],
                                    ],
                                ],
                            ],
                            'max_tokens' => 1024,
                            'stream'     => false,
                        ])->getReturn();

                    return (new Plain(Arr::get($res, 'message.content')))->setUsage(Arr::get($res, 'usage.total_tokens'));
                }
            };
        }, $this->models);
    }
}
