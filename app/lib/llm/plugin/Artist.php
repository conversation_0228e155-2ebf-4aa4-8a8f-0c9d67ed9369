<?php

namespace app\lib\llm\plugin;

use app\lib\Llm;
use app\lib\llm\contract\ToolkitInterface;
use app\lib\llm\Tool;
use app\lib\llm\tool\Args;
use app\lib\llm\tool\result\Image;
use app\lib\llm\tool\result\Json;
use think\helper\Arr;

class Artist implements ToolkitInterface
{
    protected $models;

    public function __construct($config)
    {
        $this->models = Arr::get($config, 'models', []);
    }

    public function getTools()
    {
        return array_map(function ($tool) {
            return new class($tool) extends Tool {
                protected $description = '支持根据用户输入的文字内容，生成符合语义描述的不同风格的图像';
                protected $size;

                public function __construct(protected $config)
                {
                    $this->title      = $this->config['title'];
                    $this->name       = $this->config['name'];
                    $this->parameters = [
                        'prompt' => [
                            'type'        => 'string',
                            'description' => '描述画面的提示词信息',
                            'required'    => true,
                        ],
                        'size'   => [
                            'type'        => 'string',
                            'description' => '图像尺寸，支持square,vertical,horizontal三种预设尺寸，或者自定义尺寸，如：1024x1024',
                            'enum'        => ['square', 'vertical', 'horizontal'],
                            'enumNames'   => ['方(1024x1024)', '竖屏(720x1280)', '横屏(1280x720)'],
                            'default'     => 'square',
                        ],
                    ];

                    $this->size = [
                        'square'     => Arr::get($this->config, 'size.square', '1024x1024'),
                        'vertical'   => Arr::get($this->config, 'size.vertical', '720x1280'),
                        'horizontal' => Arr::get($this->config, 'size.horizontal', '1280x720'),
                    ];

                    if (!empty($this->config['image'])) {
                        $this->parameters['image'] = [
                            'type'        => 'string',
                            'description' => '参考图片URL地址',
                        ];
                    }
                }

                public function run(Args $args)
                {
                    $size   = $args->get('size', 'square');
                    $output = $args->get('output', 'direct');

                    $data = [
                        'model'  => $this->config['code'],
                        'prompt' => $args->get('prompt'),
                        'size'   => $this->size[$size] ?? $size,
                    ];

                    if (!empty($args['image'])) {
                        $data['image'] = [
                            'url' => $args['image'],
                        ];
                    }

                    $res = app()->make(Llm::class, newInstance: true)
                        ->image()
                        ->generations($data);

                    $image = $res['images'][0];
                    $url   = Arr::get($image, 'url', Arr::get($image, 'b64_json'));

                    if ($output == 'direct') {
                        $result = new Image($url);
                    } else {
                        $result = new Json(['url' => $url]);
                    }

                    $result->setUsage($res['usage']['total_tokens']);

                    return $result;
                }
            };
        }, $this->models);
    }
}
