<?php

namespace app\lib\llm;

use app\lib\Llm;
use app\lib\llm\contract\ChatInterface;
use app\lib\llm\mcp\Toolkit;
use app\lib\llm\tool\result\Error;
use app\model\Plugin;
use think\helper\Arr;
use think\helper\Str;
use Throwable;

class ToolCall implements ChatInterface
{
    protected $tools = [];

    public function __construct(protected Llm $llm, protected ChatInterface $chat)
    {
    }

    public function completions($params)
    {
        if (!empty($params['tools'])) {
            $params['tools'] = Arr::flatMap(function ($item) {
                try {
                    if ($item['type'] == 'plugin') {
                        $pluginName = $item['plugin']['name'];
                        $plugin     = Plugin::getByName($pluginName);

                        $tool = $plugin->getTool($item['plugin']['tool']);
                        $args = $item['plugin']['args'] ?? [];

                        $name = "plugin-{$pluginName}-{$tool->getName()}";

                        $this->tools[$name] = [$pluginName, $tool, $args];

                        return [
                            [
                                'type'     => 'function',
                                'function' => [
                                    'name'        => $name,
                                    'description' => $tool->getLlmDescription(),
                                    'parameters'  => $tool->getLlmParameters(),
                                ],
                            ],
                        ];
                    } elseif ($item['type'] == 'mcp') {
                        $mcpName   = $item['mcp']['name'];
                        $url       = $item['mcp']['url'];
                        $transport = $item['mcp']['transport'] ?? 'http';
                        $allowed   = $item['mcp']['allowed'] ?? [];
                        $plugin    = new Toolkit($url, $transport);

                        $tools = [];
                        foreach ($plugin->getTools() as $tool) {
                            // 如果设置了允许的工具列表，只注册允许的工具
                            if (!empty($allowed) && !in_array($tool->getName(), $allowed)) {
                                continue;
                            }

                            $name               = "mcp-{$mcpName}-{$tool->getName()}";
                            $this->tools[$name] = ['mcp', $tool, [], $plugin, $mcpName];

                            $tools[] = [
                                'type'     => 'function',
                                'function' => [
                                    'name'        => $name,
                                    'description' => $tool->getLlmDescription(),
                                    'parameters'  => $tool->getLlmParameters(),
                                ],
                            ];
                        }

                        return $tools;
                    }
                } catch (Throwable) {
                    return [];
                }
                return [$item];
            }, $params['tools']);
        }

        if (empty($params['tools'])) {
            unset($params['tools']);
        }

        $params['messages'] = Arr::flatMap(function ($message) {
            $toolMessages = [];
            if ($message['role'] == 'assistant' && !empty($message['tool_calls'])) {
                $message['tool_calls'] = array_map(function ($call) use (&$toolMessages) {
                    if ($call['type'] == 'plugin') {
                        $call['type']     = 'function';
                        $call['function'] = [
                            'name'      => "plugin-{$call['plugin']['name']}-{$call['plugin']['tool']}",
                            'arguments' => $call['plugin']['arguments'],
                        ];

                        $toolMessages[] = [
                            'role'         => 'tool',
                            'tool_call_id' => $call['id'],
                            'content'      => $call['plugin']['response'],
                        ];
                        unset($call['plugin']);
                    } elseif ($call['type'] == 'mcp') {
                        $call['type']     = 'function';
                        $call['function'] = [
                            'name'      => "mcp-{$call['mcp']['name']}-{$call['mcp']['tool']}",
                            'arguments' => $call['mcp']['arguments'],
                        ];

                        $toolMessages[] = [
                            'role'         => 'tool',
                            'tool_call_id' => $call['id'],
                            'content'      => $call['mcp']['response'],
                        ];
                        unset($call['mcp']);
                    }
                    return $call;
                }, $message['tool_calls']);
            }
            return [$message, ...$toolMessages];
        }, $params['messages']);

        $generator = $this->chat->completions($params);

        foreach ($generator as $item) {
            if (!empty($item['delta']['tool_calls'])) {
                $call = $item['delta']['tool_calls'][0];
                if ($call['type'] == 'function' && isset($this->tools[$call['function']['name']])) {
                    foreach ($this->invokeTool($call) as $res) {
                        $item['delta']['tool_calls'] = [$res];
                        yield $item;
                    }
                    continue;
                }
            }

            yield $item;
        }

        $return = $generator->getReturn();

        if (!empty($return['message']['tool_calls'])) {
            $return['message']['tool_calls'] = array_map(function ($call) {
                if ($call['type'] == 'function' && isset($this->tools[$call['function']['name']])) {
                    $newCall = [];
                    foreach ($this->invokeTool($call) as $res) {
                        unset($res['index']);
                        $newCall = Arr::mergeDeep($newCall, $res);
                    }
                    return $newCall;
                }
                return $call;
            }, $return['message']['tool_calls']);
        }

        return $return;
    }

    protected function invokeTool($call)
    {
        $functionName = $call['function']['name'];
        $toolData     = $this->tools[$functionName];

        $toolType = match (true) {
            Str::startsWith($functionName, 'plugin-') => 'plugin',
            Str::startsWith($functionName, 'mcp-') => 'mcp',
            default => throw new \Exception('Unknown tool type'),
        };

        [$name, $tool, $args] = $toolData;

        yield [
            'index'   => $call['index'] ?? null,
            'id'      => $call['id'] ?? '',
            'type'    => $toolType,
            $toolType => [
                'name'      => $name,
                'tool'      => $tool->getName(),
                'title'     => $tool->getTitle(),
                'function'  => $call['function']['name'],
                'arguments' => $call['function']['arguments'],
            ],
        ];

        try {
            $arguments = json_decode($call['function']['arguments'], true);

            if (!is_array($arguments)) {
                $arguments = [];
            }

            /** @var \app\lib\llm\tool\Result $result */
            $result = $tool(array_merge($arguments, $args));

            if ($toolType == 'plugin') {
                //调用插件产生的计费
                $usage = $result->getUsage();

                $this->llm->consumeTokens('plugin', "{$name}-{$tool->getName()}", $usage);
            }
        } catch (Throwable $e) {
            $result = new Error($e);
        }

        yield [
            'index'   => $call['index'] ?? null,
            $toolType => [
                'content'  => $result->getContent(),
                'response' => $result->getResponse(),
                'error'    => $result instanceof Error,
                'usage'    => $usage ?? 0,
            ],
        ];
    }

}
