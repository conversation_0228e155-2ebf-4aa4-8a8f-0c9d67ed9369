<?php

namespace app\lib\llm;

use app\lib\llm\contract\ImageInterface;

class Image extends Base implements ImageInterface
{

    public function generations($params)
    {
        return $this->call($params);
    }

    public function inpainting($params)
    {
        return $this->call($params);
    }

    public function outpainting($params)
    {
        return $this->call($params);
    }

    public function upscale($params)
    {
        return $this->call($params);
    }

    public function poster($params)
    {
        return $this->call($params);
    }

    public function edit($params)
    {
        return $this->call($params);
    }
}
