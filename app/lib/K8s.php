<?php

namespace app\lib;

use K8s\Client\Options;
use K8s\HttpGuzzle\ClientFactory;
use K8s\WsSwoole\CoroutineAdapter;

class K8s extends \K8s\Client\K8s
{
    public static function inCluster()
    {
        $token     = file_get_contents('/var/run/secrets/kubernetes.io/serviceaccount/token');
        $namespace = file_get_contents('/var/run/secrets/kubernetes.io/serviceaccount/namespace');

        $options = new Options('https://kubernetes.default.svc');

        $options->setToken($token);

        if ($namespace) {
            $options->setNamespace($namespace);
        }

        $clientFactory = new ClientFactory([
            'verify' => '/var/run/secrets/kubernetes.io/serviceaccount/ca.crt',
        ]);

        $options->setHttpClientFactory($clientFactory);

        $websocket = new CoroutineAdapter([
            'ssl_allow_self_signed' => true,
            'ssl_verify_peer'       => false,
            'verify_peer'           => false,
            'verify_peer_name'      => false,
        ]);

        $options->setWebsocketClient($websocket);

        return new self($options);
    }

    /**
     * @template T
     * @param string|class-string<T> $kindFqcn
     * @return T
     */
    public function read(string $name, string $kindFqcn, $query = []): object
    {
        return parent::read($name, $kindFqcn, $query);
    }

    /**
     * @template T
     * @param T $kind
     * @param array $query
     * @param string|null $namespace
     * @return T
     */
    public function create($kind, $query = [], ?string $namespace = null): object
    {
        return parent::create($kind, $query, $namespace);
    }

    /**
     * @template T
     * @param string|class-string<T> $kindFqcn
     * @return iterable<int, T>
     */
    public function listNamespaced(string $kindFqcn, $query = [], ?string $namespace = null): iterable
    {
        return parent::listNamespaced($kindFqcn, $query, $namespace);
    }
}
