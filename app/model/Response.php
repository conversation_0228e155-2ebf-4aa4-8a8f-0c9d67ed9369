<?php

namespace app\model;

use think\annotation\model\option\Type;

/**
 * Class app\model\Response
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property array $tools
 * @property int $id
 * @property string $input
 * @property string $instructions
 * @property string $model
 * @property array $output
 * @property string $previous_id
 * @property-read mixed $hash_id
 */
#[Type('tools', 'json')]
#[Type('output', 'json')]
class Response extends \think\Model
{
    protected function getHashIdAttr()
    {
        $hashids = new \Hashids\Hashids('', 20);

        return $hashids->encode($this->id);
    }

    public static function fromHashId($hashId)
    {
        try {
            $hashids = new \Hashids\Hashids('', 20);
            [$id] = $hashids->decode($hashId);
        } catch (\Throwable) {
            return null;
        }

        return self::where('id', $id)->find();
    }
}
