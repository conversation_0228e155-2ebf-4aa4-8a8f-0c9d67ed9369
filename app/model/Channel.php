<?php

namespace app\model;

use app\lib\llm\channel\Driver;
use Symfony\Component\Finder\Finder;
use think\Model;

/**
 * Class app\model\Channel
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property mixed $auth 认证信息，可以是数组实现负载均衡
 * @property string $base_uri 基础请求地址，可选
 * @property string $driver 驱动类
 * @property string $name 渠道名称
 */
class Channel extends Model
{

    public function getAuth()
    {
        $auth = $this->auth;
        if (empty($auth)) {
            return null;
        }

        if (is_array($auth) && count($auth) > 0) {
            $randomKey = array_rand($auth);
            return $auth[$randomKey];
        }

        return $auth;
    }

    protected function getDriverAttr($driver)
    {
        if ($driver == 'tongyi') {
            //百炼兼容通义
            return 'bailian';
        }
        return $driver;
    }

    /**
     * 获取所有可用的驱动
     *
     * @return array
     */
    public static function getAvailableDrivers()
    {
        $finder = Finder::create()
            ->in(app_path('lib/llm/channel'))
            ->depth(0)
            ->directories();

        $drivers = [];

        foreach ($finder as $dir) {
            $name   = $dir->getBasename();
            $driver = "\\app\\lib\\llm\\channel\\{$name}\\Driver";

            if (class_exists($driver) && is_subclass_of($driver, Driver::class)) {
                $drivers[] = [
                    'name' => constant("{$driver}::NAME"),
                    'code' => $name,
                ];
            }
        }

        return $drivers;
    }
}
