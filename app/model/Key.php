<?php

namespace app\model;

use app\lib\Date;
use think\annotation\model\option\Type;
use think\facade\Cache;
use think\helper\Str;
use think\Model;

/**
 * Class app\model\Key
 *
 * @property \app\lib\Date $create_time
 * @property \app\lib\Date $last_time
 * @property \app\lib\Date $update_time
 * @property int $id
 * @property string $name
 * @property string $value
 */
#[Type('last_time', Date::class)]
class Key extends Model
{
    const KEY = 'api_key_%s';

    /**
     * 根据value获取API Key
     *
     * @param string $value
     * @return Key|null
     */
    public static function getByValue($value)
    {
        /** @var Key $apiKey */
        $apiKey = Cache::remember(sprintf(self::KEY, $value), function () use ($value) {
            $key = Key::where('value', $value)->find();

            if ($key) {
                $key->save([
                    'last_time' => Date::now(),
                ]);
            }

            return $key;
        }, 60 * 60); // 缓存60分钟

        return $apiKey;
    }

    /**
     * 生成新的API Key值
     *
     * @return string
     */
    public static function generateValue()
    {
        // 固定前缀
        $prefix = 'sk-';

        // 使用Str::random生成随机字符串
        $randomString = Str::random(32);

        return $prefix . $randomString;
    }
}
