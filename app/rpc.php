<?php

/**
 * This file is auto-generated.
 */

declare(strict_types=1);

namespace rpc\contract\cloud;

use think\swoole\rpc\client\Service;

interface Api extends Service
{
	public function check(string $name);


	public function report(string $name, string $path, bool $success, ?string $id);
}

interface Sms extends Service
{
	public function send($signId, $templateId, string $phone, ?string $params);


	public function batchSend($signId, $templateId, string $phone, ?string $params);


	public function queryStatus($id);


	public function addSign($name, $source, $company = null, $proof = null);


	public function modifySign($id, $name, $source, $company = null, $proof = null);


	public function querySign($id);


	public function deleteSign($id);


	public function addTemplate($name, $type, $signId, $content);


	public function modifyTemplate($id, $name, $type, $signId, $content);


	public function queryTemplate($id);


	public function deleteTemplate($id);
}

interface Ai extends Service
{
	public function check();


	public function consumeTokens(string $type, string $code, int $usage);
}


namespace rpc\contract\api;

use think\swoole\rpc\client\Service;

interface Api extends Service
{
	public function getList();


	public function getInfo($name);


	public function run($uri, $params = []);


	public function meta($uri);
}
return [
	'cloud' => ['rpc\contract\cloud\Api', 'rpc\contract\cloud\Sms', 'rpc\contract\cloud\Ai'],
	'api' => ['rpc\contract\api\Api'],
];