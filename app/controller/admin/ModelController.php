<?php

namespace app\controller\admin;

use app\model\Channel;
use app\model\Model;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\annotation\route\Resource;
use yunwuxin\auth\middleware\Authentication;

#[Resource('model')]
#[Middleware(Authentication::class)]
class ModelController extends Controller
{
    public function index($type)
    {
        $models = Model::where('type', $type)->with(['channel'])->order('sort asc,channel_id desc')->paginate();

        return json($models);
    }

    public function save()
    {
        $data = $this->validate([
            'channel_id'  => 'require',
            'code'        => 'require',
            'label'       => 'require',
            'type'        => 'require',
            'description' => 'require',
            'factor'      => 'require',
            'version'     => '',
            'params'      => '',
            'sort'        => '',
            'status'      => '',
        ]);

        return Model::create($data);
    }

    public function update($id)
    {
        $model = Model::findOrFail($id);

        $data = $this->validate([
            'channel_id'  => 'require',
            'code'        => 'require',
            'label'       => 'require',
            'description' => 'require',
            'factor'      => 'require',
            'version'     => '',
            'params'      => '',
            'sort'        => '',
        ]);

        return $model->save($data);
    }

    #[Post('model/:id/status')]
    public function status($id)
    {
        $model = Model::findOrFail($id);

        $data = $this->validate([
            'status' => '',
        ]);

        return $model->save($data);
    }

    public function delete($id)
    {
        $model = Model::findOrFail($id);

        return $model->delete();
    }

    #[Get('model/channels')]
    public function channels()
    {
        // 返回所有渠道
        return Channel::order('id desc')->select()->map(function (Channel $channel) {
            return [
                'label' => $channel->name,
                'value' => $channel->id,
            ];
        });
    }
}
