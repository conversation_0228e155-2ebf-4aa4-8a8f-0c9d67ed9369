<?php

namespace app\controller\admin;

use app\model\Key;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Post;
use think\annotation\route\Resource;
use think\facade\Cache;
use yunwuxin\auth\middleware\Authentication;

#[Resource('key')]
#[Pattern('id', '\d+')]
#[Middleware(Authentication::class)]
class KeyController extends Controller
{
    /**
     * 获取API Key列表
     */
    #[Get('key')]
    public function index()
    {
        $query = Key::order('id', 'desc');

        return $query->paginate();
    }

    /**
     * 创建API Key
     */
    #[Post('key')]
    public function save()
    {
        $data = $this->validate([
            'name|名称' => 'require',
        ]);

        // 生成API Key值
        $data['value'] = Key::generateValue();

        Key::create($data);
    }

    /**
     * 删除API Key
     */
    public function delete($id)
    {
        $key = Key::findOrFail($id);

        // 删除缓存
        if ($key->value) {
            Cache::delete(sprintf(Key::KEY, $key->value));
        }

        $key->delete();
    }

}
