<?php

namespace app\controller\admin;

use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\Filesystem;
use yunwuxin\auth\middleware\Authentication;

#[Middleware(Authentication::class)]
class UploadController extends Controller
{
    #[Post('upload/:dir')]
    public function save(Filesystem $filesystem)
    {
        $data = $this->validate([
            'dir'  => 'require|in:model,plugin',
            'file' => 'require',
        ]);

        $disk = $filesystem->disk('uploads');

        $path = $disk->putFile($data['dir'], $data['file']);

        return json([
            'url' => $disk->url($path),
        ]);
    }
}
