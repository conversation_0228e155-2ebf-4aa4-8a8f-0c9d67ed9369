<?php

namespace app\controller\admin;

use app\model\Admin;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Resource;
use think\exception\ValidateException;
use yunwuxin\auth\middleware\Authentication;

#[Resource('admin')]
#[Pattern('id', '\d+')]
#[Middleware(Authentication::class)]
class AdminController extends Controller
{
    /**
     * 获取管理员列表
     */
    public function index()
    {
        return Admin::order('id desc')->paginate();
    }

    /**
     * 创建管理员
     */
    public function save()
    {
        $data = $this->validate([
            'username' => 'require|unique:admin',
            'password' => 'require|min:6',
        ]);

        $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);

        return Admin::create($data);
    }

    /**
     * 更新管理员
     */
    public function update($id)
    {
        $admin = Admin::findOrFail($id);

        $data = $this->validate([
            'username' => 'require|unique:admin,username,' . $id,
            'password' => 'min:6',
        ]);

        // 如果提供了新密码，则更新密码
        if (!empty($data['password'])) {
            $data['password'] = password_hash($data['password'], PASSWORD_DEFAULT);
        } else {
            // 如果没有提供密码，则不更新密码字段
            unset($data['password']);
        }

        return $admin->save($data);
    }

    /**
     * 删除管理员
     */
    public function delete($id)
    {
        // 不允许删除ID为1的管理员（系统默认管理员）
        if ($id == 1) {
            throw new ValidateException('不能删除系统默认管理员');
        }

        $admin = Admin::findOrFail($id);
        return $admin->delete();
    }
}
