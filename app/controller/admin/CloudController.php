<?php

namespace app\controller\admin;

use app\model\Admin;
use think\annotation\route\Get;
use think\annotation\route\Post;
use TopThinkCloud\Client;
use TopThinkCloud\OAuth;

class CloudController extends Controller
{

    #[Get('cloud/login')]
    public function index(OAuth $auth)
    {
        return [
            'url' => $auth->getAuthorizeUrl(),
        ];
    }

    #[Post('cloud/login')]
    public function save(Client $client)
    {
        $data = $this->validate([
            'token' => 'require',
        ]);

        $client->authenticate($data['token']);

        $userInfo = $client->currentUser()->info();

        if (!$userInfo['is_admin']) {
            abort(403, '无权限');
        }

        $admin = Admin::where('id', $userInfo['id'])->find();

        if (!$admin) {
            $admin = new Admin([
                'id' => $userInfo['id'],
            ]);
        }

        $admin->save([
            'username' => $userInfo['email'],
        ]);

        return [
            'token' => $admin->token,
        ];
    }

}
