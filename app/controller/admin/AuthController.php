<?php

namespace app\controller\admin;

use app\model\Admin;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Post;
use think\exception\ValidateException;
use yunwuxin\Auth;
use yunwuxin\auth\middleware\Authentication;

class AuthController extends Controller
{
    #[Get('auth/current')]
    #[Middleware(Authentication::class)]
    public function current(Auth $auth)
    {
        return $auth->user()->append(['token']);
    }

    #[Post('auth/login')]
    public function login()
    {
        $data = $this->validate([
            'username|用户名' => 'require',
            'password|密码'   => 'require',
        ]);

        $admin = Admin::where('username', $data['username'])->find();

        if ($admin && password_verify($data['password'], $admin->password)) {
            return [
                'token' => $admin->token,
            ];
        }

        throw new ValidateException('用户名或密码不正确');
    }
}
