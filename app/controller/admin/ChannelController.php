<?php

namespace app\controller\admin;

use app\model\Channel;
use think\annotation\route\Get;
use think\annotation\route\Middleware;
use think\annotation\route\Pattern;
use think\annotation\route\Resource;
use yunwuxin\auth\middleware\Authentication;

#[Resource('channel')]
#[Pattern('id', '\d+')]
#[Middleware(Authentication::class)]
class ChannelController extends Controller
{
    /**
     * 获取渠道列表
     */
    public function index()
    {
        return Channel::order('id desc')->paginate();
    }

    /**
     * 创建渠道
     */
    public function save()
    {
        $data = $this->validate([
            'name'     => 'require',
            'driver'   => 'require',
            'auth'     => 'require',
            'base_uri' => '',
        ]);

        return Channel::create($data);
    }

    /**
     * 更新渠道
     */
    public function update($id)
    {
        $channel = Channel::findOrFail($id);

        $data = $this->validate([
            'name'     => 'require',
            'driver'   => 'require',
            'auth'     => 'require',
            'base_uri' => '',
        ]);

        return $channel->save($data);
    }

    /**
     * 删除渠道
     */
    public function delete($id)
    {
        $channel = Channel::findOrFail($id);
        return $channel->delete();
    }

    /**
     * 获取可用的驱动列表
     */
    #[Get('channel/drivers')]
    public function drivers()
    {
        $drivers = Channel::getAvailableDrivers();

        return array_map(function ($driver) {
            return [
                'label' => $driver['name'],
                'value' => $driver['code'],
            ];
        }, $drivers);
    }

}
