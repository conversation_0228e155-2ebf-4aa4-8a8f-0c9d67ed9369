<?php

namespace app\controller\admin;

use app\lib\License;
use think\annotation\route\Get;

class ManifestController extends Controller
{
    #[Get('/manifest')]
    public function index(License $license)
    {
        if (!config('cloud.enable') && !$license->isValid()) {
            abort(403, '应用尚未授权');
        }

        return [
            'cloud' => config('cloud.enable'),
        ];
    }
}
