<?php

namespace app\controller\api;

use app\lib\llm\Agent;
use think\annotation\route\Post;
use function think\swoole\helper\iterator;

class ResponsesController extends Controller
{
    #[Post('responses')]
    public function index()
    {
        $data = $this->validate([
            'previous_id'  => '',
            'model'        => 'require',
            'input'        => 'require',
            'instructions' => '',
            'tools'        => '',
        ]);

        $agent = new Agent($this->llm, $data);

        $result = $agent->run();

        $result->rewind();
        $generator = function () use ($result) {
            while ($result->valid()) {
                yield 'data: ' . json_encode($result->current()) . "\n\n";
                $result->next();
            }
            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }
}
