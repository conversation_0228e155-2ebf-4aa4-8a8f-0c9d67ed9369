<?php

namespace app\controller\api;

use think\annotation\route\Post;

class AudioController extends Controller
{
    #[Post('audio/speech')]
    public function speech()
    {
        $params = $this->validate([
            'model' => 'require',
            'input' => 'require',
            'voice' => 'require',
            'speed' => 'float',
        ]);

        $result = $this->llm->audio()->speech($params);

        return json($result);
    }

    #[Post('audio/transcriptions')]
    public function transcriptions()
    {
        $params = $this->validate([
            'model'    => 'require',
            'url'      => 'require',
            'language' => '',
        ]);

        $result = $this->llm->audio()->transcriptions($params);

        return json($result);
    }
}
