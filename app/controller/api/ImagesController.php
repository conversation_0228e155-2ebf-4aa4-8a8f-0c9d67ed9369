<?php

namespace app\controller\api;

use think\annotation\route\Post;

class ImagesController extends Controller
{
    #[Post('images/generations')]
    public function generations()
    {
        $params = $this->validate([
            'prompt'          => 'require',
            'model'           => 'require',
            'image'           => '',
            'quality'         => '',
            'seed'            => '',
            'size'            => '',
            'ratio'           => '',
            'n'               => '',
            'style'           => '',
            'response_format' => '',
        ]);

        $result = $this->llm->image()->generations($params);

        return json($result);
    }

    #[Post('images/poster')]
    public function poster()
    {
        $params = $this->validate([
            'model'     => 'require',
            'title'     => 'require',
            'sub_title' => '',
            'body'      => '',
            'prompt'    => '',
            'n'         => '',
            'style'     => '',
            'size'      => '',
        ]);

        $result = $this->llm->image()->poster($params);

        return json($result);
    }

    #[Post('images/inpainting')]
    public function inpainting()
    {
        $params = $this->validate([
            'model'           => 'require',
            'image'           => 'require',
            'mask'            => 'require',
            'prompt'          => '',
            'response_format' => '',
        ]);

        $result = $this->llm->image()->inpainting($params);

        return json($result);
    }

    #[Post('images/outpainting')]
    public function outpainting()
    {
        $params = $this->validate([
            'model'           => 'require',
            'image'           => 'require',
            'prompt'          => '',
            'top'             => '',
            'bottom'          => '',
            'left'            => '',
            'right'           => '',
            'response_format' => '',
        ]);

        $result = $this->llm->image()->outpainting($params);

        return json($result);
    }

    #[Post('images/upscale')]
    public function upscale()
    {
        $params = $this->validate([
            'model'           => 'require',
            'image'           => 'require',
            'response_format' => '',
        ]);

        $result = $this->llm->image()->upscale($params);

        return json($result);
    }

    #[Post('images/edit')]
    public function edit()
    {
        $params = $this->validate([
            'model'           => 'require',
            'image'           => 'require',
            'prompt'          => 'require',
            'response_format' => '',
        ]);

        $result = $this->llm->image()->edit($params);

        return json($result);
    }
}
