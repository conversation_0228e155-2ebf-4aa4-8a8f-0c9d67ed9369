<?php

namespace app\controller\api;

use think\annotation\route\Post;

class RerankController extends Controller
{
    #[Post('rerank')]
    public function index()
    {
        $params = $this->validate([
            'model'     => 'require',
            'query'     => 'require',
            'documents' => 'require',
            'score'     => '',
        ]);

        $result = $this->llm->text()->rerank($params);

        return json($result);
    }
}
