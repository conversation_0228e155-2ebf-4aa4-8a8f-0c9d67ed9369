<?php

namespace app\controller\api;

use think\annotation\route\Post;
use function think\swoole\helper\iterator;

class ChatController extends Controller
{
    #[Post('chat/completions')]
    public function completions()
    {
        $params = $this->validate([
            'model'            => 'require',
            'messages'         => 'require|array',
            'tools'            => 'array',
            'max_tokens'       => 'integer',
            'thinking'         => 'in:enabled,disabled,auto',
            'temperature'      => 'float',
            'moderation'       => 'bool',
            'stream'           => 'bool',
            'user'             => '',
            'prompt_cache_key' => '',
            'response_format'  => '',
        ]);

        $result = $this->llm->chat()->completions($params);

        $result->rewind();

        if (!$result->valid()) {
            return json($result->getReturn());
        }

        $generator = function () use ($result) {
            foreach ($result as $event) {
                yield 'data: ' . json_encode($event) . "\n\n";
            }

            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }
}
