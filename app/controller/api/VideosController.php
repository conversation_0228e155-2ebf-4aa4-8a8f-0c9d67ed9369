<?php

namespace app\controller\api;

use think\annotation\route\Post;

class VideosController extends Controller
{
    #[Post('videos/generations')]
    public function generations()
    {
        $params = $this->validate([
            'model'      => 'require',
            'prompt'     => '',
            'image'      => '',
            'resolution' => '',
            'ratio'      => '',
            'size'       => '',
            'duration'   => '',
            'quality'    => '',
        ]);

        $result = $this->llm->video()->generations($params);

        return json($result);
    }

    #[Post('videos/query')]
    public function query()
    {
        $params = $this->validate([
            'model' => 'require',
            'id'    => 'require',
        ]);

        $result = $this->llm->video()->query($params);

        return json($result);
    }
}
