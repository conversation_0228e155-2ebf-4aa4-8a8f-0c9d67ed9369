<?php

namespace app\controller\api;

use think\annotation\route\Post;

class MusicController extends Controller
{
    #[Post('music/song')]
    public function song()
    {
        $params = $this->validate([
            'model'    => 'require',
            'prompt'   => '',
            'lyrics'   => '',
            'duration' => '',
        ]);

        $result = $this->llm->music()->song($params);

        return json($result);
    }

    #[Post('music/bgm')]
    public function bgm()
    {
        $params = $this->validate([
            'model'    => 'require',
            'prompt'   => 'require',
            'duration' => '',
        ]);

        $result = $this->llm->music()->bgm($params);

        return json($result);
    }

    #[Post('music/query')]
    public function query()
    {
        $params = $this->validate([
            'model' => 'require',
            'id'    => 'require',
        ]);

        $result = $this->llm->music()->query($params);

        return json($result);
    }
}
