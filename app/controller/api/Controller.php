<?php

namespace app\controller\api;

use app\BaseController;
use app\lib\License;
use app\lib\Llm;
use app\model\Key;
use rpc\contract\cloud\Ai;
use think\App;
use think\helper\Str;

abstract class Controller extends BaseController
{
    /**
     * 未授权访问错误信息
     */
    private const UNAUTHORIZED_MESSAGE = '未授权访问';

    public function __construct(App $app, protected Llm $llm, License $license)
    {
        parent::__construct($app);

        if (!config('cloud.enable') && !$license->isValid()) {
            abort(403, '应用尚未授权');
        }

        $this->authenticateRequest();
    }

    /**
     * 验证请求并设置相应的认证
     *
     * @return void
     */
    private function authenticateRequest(): void
    {
        $token = $this->getTokenFromRequest();
        if (empty($token)) {
            abort(401, self::UNAUTHORIZED_MESSAGE);
        }

        // API Key 认证
        if (Str::startsWith($token, 'sk-')) {
            $key = Key::getByValue($token);
            if (empty($key)) {
                abort(401, self::UNAUTHORIZED_MESSAGE);
            }
            return;
        }

        // 云服务认证
        if (config('cloud.enable')) {
            $ai = $this->app->make(Ai::class);
            $ai->withContext([
                'token' => $token,
            ]);

            $ai->check();
            $this->llm->setMeter([$ai, 'consumeTokens']);
            return;
        }

        // 无有效认证方式
        abort(401, self::UNAUTHORIZED_MESSAGE);
    }

    /**
     * 从请求中获取认证令牌
     *
     * @return string|null
     */
    public function getTokenFromRequest()
    {
        $header = $this->request->header('Authorization');
        return (!empty($header) && Str::startsWith($header, 'Bearer '))
            ? Str::substr($header, 7)
            : null;
    }
}
