<?php

namespace app\controller\api\compatible;

use app\controller\api\Controller;
use think\annotation\route\Post;
use function think\swoole\helper\iterator;

class OpenaiController extends Controller
{
    #[Post('openai/v1/chat/completions')]
    public function completions()
    {
        $params = $this->validate([
            'model'             => 'require',
            'messages'          => 'require|array',
            'tools'             => 'array',
            'tool_choice'       => '',
            'max_tokens'        => 'integer',
            'temperature'       => 'float',
            'top_p'             => 'float',
            'frequency_penalty' => 'float',
            'presence_penalty'  => 'float',
            'stop'              => '',
            'stream'            => 'bool',
            'user'              => '',
            'seed'              => 'integer',
            'response_format'   => '',
            'prompt_cache_key'  => '',
        ]);

        $params['thinking'] = 'disabled';

        $result = $this->llm->chat()->completions($params);

        $result->rewind();

        if (!$result->valid()) {
            // 非流式响应，转换为OpenAI格式
            return json($this->convertResponseToOpenAI($result->getReturn(), $params));
        }

        $generator = function () use ($result, $params) {
            $chatId = 'chatcmpl-' . uniqid();
            $model  = $params['model'];

            foreach ($result as $event) {
                $openaiEvent = $this->convertEventToOpenAI($event, $chatId, $model);
                if ($openaiEvent) {
                    yield 'data: ' . json_encode($openaiEvent) . "\n\n";
                }
            }

            yield "data: [DONE]\n\n";
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }

    /**
     * 将系统内部事件格式转换为OpenAI流式格式
     */
    private function convertEventToOpenAI($event, $chatId, $model)
    {
        // 处理usage事件（通常是最后的事件）
        if (isset($event['usage'])) {
            return [
                'id'      => $chatId,
                'object'  => 'chat.completion.chunk',
                'created' => time(),
                'model'   => $model,
                'choices' => [
                    [
                        'index'         => 0,
                        'delta'         => [],
                        'finish_reason' => $event['finish_reason'] ?? 'stop',
                    ],
                ],
                'usage'   => [
                    'prompt_tokens'     => $event['usage']['prompt_tokens'] ?? 0,
                    'completion_tokens' => $event['usage']['completion_tokens'] ?? 0,
                    'total_tokens'      => $event['usage']['total_tokens'] ?? 0,
                ],
            ];
        }

        // 处理delta事件
        if (isset($event['delta'])) {
            return [
                'id'      => $chatId,
                'object'  => 'chat.completion.chunk',
                'created' => time(),
                'model'   => $model,
                'choices' => [
                    [
                        'index'         => 0,
                        'delta'         => $event['delta'],
                        'finish_reason' => null,
                    ],
                ],
            ];
        }

        return null;
    }

    /**
     * 将系统内部响应格式转换为OpenAI非流式格式
     */
    private function convertResponseToOpenAI($response, $params)
    {
        $chatId = 'chatcmpl-' . uniqid();
        $model  = $params['model'] ?? 'gpt-3.5-turbo';

        return [
            'id'      => $chatId,
            'object'  => 'chat.completion',
            'created' => time(),
            'model'   => $model,
            'choices' => [
                [
                    'index'         => 0,
                    'message'       => $response['message'] ?? [],
                    'finish_reason' => $response['finish_reason'] ?? 'stop',
                ],
            ],
            'usage'   => [
                'prompt_tokens'     => $response['usage']['prompt_tokens'] ?? 0,
                'completion_tokens' => $response['usage']['completion_tokens'] ?? 0,
                'total_tokens'      => $response['usage']['total_tokens'] ?? 0,
            ],
        ];
    }
}
