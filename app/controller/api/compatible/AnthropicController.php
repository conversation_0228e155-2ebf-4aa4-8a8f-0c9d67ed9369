<?php

namespace app\controller\api\compatible;

use app\controller\api\Controller;
use app\model\Model;
use think\annotation\route\Post;
use function think\swoole\helper\iterator;

class AnthropicController extends Controller
{
    #[Post('anthropic/v1/messages')]
    public function messages()
    {
        $params = $this->validate([
            'model'          => 'require',
            'system'         => '',
            'messages'       => 'require|array',
            'tools'          => 'array',
            'tool_choice'    => 'array',
            // 'max_tokens'     => 'integer', //忽略max_tokens限制，glm4.5v的比较小 限制会出错
            'temperature'    => 'float',
            'top_k'          => 'integer',
            'top_p'          => 'float',
            'stop_sequences' => 'array',
            'stream'         => 'bool',
            'thinking'       => 'array',
            'metadata'       => 'array',
        ]);

        $userAgent   = $this->request->header('user-agent');
        $isClaudeCli = strpos($userAgent, 'claude-cli') !== false;

        if ($isClaudeCli) {
            $model = Model::where('code', $params['model'])->where('type', 'chat')->find();
            if (empty($model)) {
                $params['model'] = empty($params['tools']) ? 'claude-haiku' : 'claude-sonnet';
            } else {
                $params['model'] = $model;
            }
        }

        // 转换 Anthropic 格式到 OpenAI 格式
        $params = $this->convertAnthropicToOpenAI($params);

        $result = $this->llm->chat()->completions($params);

        $result->rewind();

        if (!$result->valid()) {
            // 非流式响应，直接转换结果为 Anthropic 格式
            return json($this->convertResponseToAnthropic($result->getReturn()));
        }

        $generator = function () use ($result, $params) {
            $messageId         = 'msg_' . uniqid();
            $hasStartedContent = false;
            $currentIndex      = 0;

            // 发送 message_start 事件
            yield "event: message_start\n";
            yield 'data: ' . json_encode([
                    'type'    => 'message_start',
                    'message' => [
                        'id'            => $messageId,
                        'type'          => 'message',
                        'role'          => 'assistant',
                        'content'       => [],
                        'model'         => $params['model'] instanceof Model ? $params['model']->code : $params['model'],
                        'stop_reason'   => null,
                        'stop_sequence' => null,
                        'usage'         => [
                            'input_tokens'  => 0,
                            'output_tokens' => 1,
                        ],
                    ],
                ]) . "\n\n";

            foreach ($result as $event) {
                $anthropicEvents = $this->convertEventToAnthropic($event, $hasStartedContent, $currentIndex);

                foreach ($anthropicEvents as $anthropicEvent) {
                    if ($anthropicEvent) {
                        yield "event: " . $anthropicEvent['type'] . "\n";
                        yield 'data: ' . json_encode($anthropicEvent) . "\n\n";
                    }
                }
            }
        };

        $response = iterator($generator());

        return $response->header([
            'Content-Type'      => 'text/event-stream',
            'Cache-Control'     => 'no-cache, must-revalidate',
            'X-Accel-Buffering' => 'no',
        ]);
    }

    public function getTokenFromRequest()
    {
        $apiKey = $this->request->header('x-api-key');
        if ($apiKey) {
            return $apiKey;
        }

        return parent::getTokenFromRequest();
    }

    /**
     * 将 Anthropic 格式转换为 OpenAI 格式
     */
    private function convertAnthropicToOpenAI($params)
    {
        $openaiParams = $params;

        // 处理 messages 格式转换
        if (!empty($params['messages'])) {
            $openaiParams['messages'] = $this->convertMessages($params['messages']);
        }

        // 处理 system 消息
        if (!empty($params['system'])) {
            // 将 system 参数转换为 messages 中的 system 消息
            $systemMessage = [
                'role'    => 'system',
                'content' => is_array($params['system']) ? $this->convertSystemContent($params['system']) : $params['system'],
            ];

            // 将 system 消息插入到 messages 开头
            array_unshift($openaiParams['messages'], $systemMessage);

            // 移除单独的 system 参数
            unset($openaiParams['system']);
        }

        // 处理 tools 格式转换
        if (!empty($params['tools'])) {
            $openaiParams['tools'] = $this->convertTools($params['tools']);
        }

        // 处理 tool_choice 转换
        if (!empty($params['tool_choice'])) {
            $openaiParams['tool_choice'] = $this->convertToolChoice($params['tool_choice']);
        }

        // 处理 stop_sequences 转换为 stop
        if (!empty($params['stop_sequences'])) {
            $openaiParams['stop'] = $params['stop_sequences'];
            unset($openaiParams['stop_sequences']);
        }

        // 处理 thinking 参数
        if (!empty($params['thinking'])) {
            $openaiParams['thinking'] = $this->convertThinking($params['thinking']);
        } else {
            $openaiParams['thinking'] = 'disabled';
        }

        // 处理 metadata 参数
        if (!empty($params['metadata'])) {
            // OpenAI 格式中没有直接对应的 metadata，可以保留或忽略
            // 这里选择保留，让底层处理
            if (!empty($params['metadata']['user_id'])) {
                $openaiParams['user']             = $params['metadata']['user_id'];
                $openaiParams['prompt_cache_key'] = $params['metadata']['user_id'];
            }
        }

        if (empty($params['stream'])) {
            $openaiParams['stream'] = false;
        }

        return $openaiParams;
    }

    /**
     * 转换 system 内容格式
     */
    private function convertSystemContent($systemContent)
    {
        if (is_array($systemContent)) {
            $text = '';
            foreach ($systemContent as $item) {
                if (isset($item['type']) && $item['type'] === 'text') {
                    $text .= $item['text'] ?? '';
                }
            }
            return $text;
        }
        return $systemContent;
    }

    /**
     * 转换消息格式
     */
    private function convertMessages($messages)
    {
        return array_map(function ($message) {
            $role    = $message['role'] ?? '';
            $content = $message['content'] ?? '';

            switch ($role) {
                case 'user':
                    // Anthropic 用户消息可能包含复杂内容结构
                    if (is_array($content)) {
                        // 检查是否包含工具结果
                        $hasToolResult = false;
                        $toolCallId    = null;
                        $toolContent   = '';

                        foreach ($content as $item) {
                            if (isset($item['type']) && $item['type'] === 'tool_result') {
                                $hasToolResult = true;
                                $toolCallId    = $item['tool_use_id'];
                                $toolContent   = $item['content'] ?? '';
                                break;
                            }
                        }

                        // 如果包含工具结果，转换为 OpenAI 的 tool 角色消息
                        if ($hasToolResult) {
                            $message['role']         = 'tool';
                            $message['tool_call_id'] = $toolCallId;
                            $message['content']      = $toolContent;
                        } else {
                            // 处理普通用户消息内容（图片等）
                            $content            = array_map(function ($item) {
                                // 转换图片格式
                                if (isset($item['type']) && $item['type'] === 'image') {
                                    $imageUrl = '';
                                    if (isset($item['source'])) {
                                        $source = $item['source'];
                                        if ($source['type'] === 'base64') {
                                            $imageUrl = 'data:' . $source['media_type'] . ';base64,' . $source['data'];
                                        } elseif (isset($source['url'])) {
                                            $imageUrl = $source['url'];
                                        }
                                    }
                                    return [
                                        'type'      => 'image_url',
                                        'image_url' => [
                                            'url' => $imageUrl,
                                        ],
                                    ];
                                }

                                unset($item['cache_control']);
                                return $item;
                            }, $content);
                            $message['content'] = $content;
                        }
                    }
                    break;

                case 'assistant':
                    // 处理 assistant 消息中的 tool_use
                    if (is_array($content)) {
                        $textContent = '';
                        $toolCalls   = [];

                        foreach ($content as $item) {
                            if (isset($item['type'])) {
                                switch ($item['type']) {
                                    case 'text':
                                        $textContent .= $item['text'] ?? '';
                                        break;
                                    case 'tool_use':
                                        $toolCalls[] = [
                                            'id'       => $item['id'],
                                            'type'     => 'function',
                                            'function' => [
                                                'name'      => $item['name'],
                                                'arguments' => json_encode($item['input'] ?? []),
                                            ],
                                        ];
                                        break;
                                }
                            }
                        }

                        $message['content'] = $textContent;
                        if (!empty($toolCalls)) {
                            $message['tool_calls'] = $toolCalls;
                        }
                    }
                    break;
            }

            return $message;
        }, $messages);
    }

    /**
     * 转换工具格式
     */
    private function convertTools($tools)
    {
        return array_map(function ($tool) {
            // Anthropic 工具格式转换为 OpenAI 格式
            if (isset($tool['name']) && isset($tool['input_schema'])) {
                unset($tool['input_schema']['$schema']);
                unset($tool['input_schema']['additionalProperties']);
                return [
                    'type'     => 'function',
                    'function' => [
                        'name'        => $tool['name'],
                        'description' => $tool['description'] ?? '',
                        'parameters'  => $tool['input_schema'],
                    ],
                ];
            }

            // 如果已经是 OpenAI 格式，直接返回
            return $tool;
        }, $tools);
    }

    /**
     * 转换 tool_choice 格式
     */
    private function convertToolChoice($toolChoice)
    {
        // Anthropic 的 tool_choice 格式
        if (isset($toolChoice['type'])) {
            switch ($toolChoice['type']) {
                case 'auto':
                    return 'auto';
                case 'any':
                    return 'required';
                case 'tool':
                    if (isset($toolChoice['name'])) {
                        return [
                            'type'     => 'function',
                            'function' => [
                                'name' => $toolChoice['name'],
                            ],
                        ];
                    }
                    break;
                case 'none':
                    return 'none';
            }
        }

        return $toolChoice;
    }

    /**
     * 转换 thinking 参数
     */
    private function convertThinking($thinking)
    {
        // Anthropic 的 thinking 格式转换
        if (isset($thinking['type'])) {
            switch ($thinking['type']) {
                case 'enabled':
                    return 'enabled';
                case 'disabled':
                    return 'disabled';
            }
        }

        return $thinking;
    }

    /**
     * 转换流式事件为 Anthropic 格式
     */
    private function convertEventToAnthropic($event, &$hasStartedContent, &$currentIndex = 0)
    {
        $events = [];
        // 处理 usage 事件 - 这通常是最后的事件
        if (isset($event['usage'])) {
            // 如果还没有结束内容块，先结束它
            if ($hasStartedContent) {
                $events[] = [
                    'type'  => 'content_block_stop',
                    'index' => $currentIndex,
                ];
            } else {
                // 如果没有开始过任何内容块，说明可能是纯工具调用
                // 需要结束当前的工具调用块
                $events[] = [
                    'type'  => 'content_block_stop',
                    'index' => $currentIndex,
                ];
            }

            $events[] = [
                'type'  => 'message_delta',
                'delta' => [
                    'stop_reason'   => $this->convertStopReason($event['finish_reason'] ?? 'stop'),
                    'stop_sequence' => null,
                ],
                'usage' => [
                    'input_tokens'  => $event['usage']['prompt_tokens'] ?? 0,
                    'output_tokens' => $event['usage']['completion_tokens'] ?? 0,
                ],
            ];

            $events[] = [
                'type' => 'message_stop',
            ];

            return $events;
        }

        // 处理 delta 事件
        if (isset($event['delta'])) {
            $delta = $event['delta'];

            // 处理文本内容
            if (isset($delta['content']) && $delta['content'] !== '') {
                // 如果还没有开始内容块，先开始它
                if (!$hasStartedContent) {
                    $events[]          = [
                        'type'          => 'content_block_start',
                        'index'         => $currentIndex,
                        'content_block' => [
                            'type' => 'text',
                            'text' => '',
                        ],
                    ];
                    $hasStartedContent = true;
                }

                $events[] = [
                    'type'  => 'content_block_delta',
                    'index' => $currentIndex,
                    'delta' => [
                        'type' => 'text_delta',
                        'text' => $delta['content'],
                    ],
                ];

                return $events;
            }

            //TODO 处理思考内容

            // 处理工具调用
            if (isset($delta['tool_calls'])) {
                $toolCall = $delta['tool_calls'][0];

                // 如果有文本内容块在进行，先结束它并移动到下一个索引
                if ($hasStartedContent) {
                    $events[] = [
                        'type'  => 'content_block_stop',
                        'index' => $currentIndex,
                    ];
                    $currentIndex++; // 移动到下一个索引
                    $hasStartedContent = false;
                }

                // 如果是新的工具调用，开始工具使用块
                if (isset($toolCall['id'])) {
                    $events[] = [
                        'type'          => 'content_block_start',
                        'index'         => $currentIndex,
                        'content_block' => [
                            'type'  => 'tool_use',
                            'id'    => $toolCall['id'],
                            'name'  => $toolCall['function']['name'] ?? '',
                            'input' => "{}",
                        ],
                    ];
                }

                // 发送工具调用的参数增量
                if (isset($toolCall['function']['arguments'])) {
                    $events[] = [
                        'type'  => 'content_block_delta',
                        'index' => $currentIndex,
                        'delta' => [
                            'type'         => 'input_json_delta',
                            'partial_json' => $toolCall['function']['arguments'],
                        ],
                    ];
                }

                return $events;
            }
        }

        // 处理完成事件
        if (isset($event['finish_reason'])) {
            if ($hasStartedContent) {
                return [
                    [
                        'type'  => 'content_block_stop',
                        'index' => $currentIndex,
                    ],
                ];
            }
        }

        return [];
    }

    /**
     * 转换完整响应为 Anthropic 格式
     */
    private function convertResponseToAnthropic($result)
    {
        $content    = [];
        $usage      = null;
        $stopReason = 'end_turn';
        $messageId  = 'msg_' . uniqid();

        // 处理非流式响应的直接结果
        if (isset($result['message'])) {
            $message = $result['message'];

            // 处理文本内容
            if (!empty($message['content'])) {
                $content[] = [
                    'type' => 'text',
                    'text' => $message['content'],
                ];
            }

            // 处理工具调用
            if (!empty($message['tool_calls'])) {
                foreach ($message['tool_calls'] as $toolCall) {
                    $content[] = [
                        'type'  => 'tool_use',
                        'id'    => $toolCall['id'],
                        'name'  => $toolCall['function']['name'],
                        'input' => json_decode($toolCall['function']['arguments'] ?? '{}', true),
                    ];
                }
            }
        }

        if (isset($result['usage'])) {
            $usage = [
                'input_tokens'  => $result['usage']['prompt_tokens'] ?? 0,
                'output_tokens' => $result['usage']['completion_tokens'] ?? 0,
            ];
        }

        if (isset($result['finish_reason'])) {
            $stopReason = $this->convertStopReason($result['finish_reason']);
        }

        return [
            'id'            => $messageId,
            'type'          => 'message',
            'role'          => 'assistant',
            'content'       => $content,
            'model'         => $this->request->param('model', 'claude-3-sonnet'),
            'stop_reason'   => $stopReason,
            'stop_sequence' => null,
            'usage'         => $usage,
        ];
    }

    /**
     * 转换停止原因
     */
    private function convertStopReason($openaiStopReason)
    {
        return match ($openaiStopReason) {
            'stop' => 'end_turn',
            'length' => 'max_tokens',
            'tool_calls' => 'tool_use',
            'content_filter' => 'refusal',
            default => 'end_turn'
        };
    }
}
